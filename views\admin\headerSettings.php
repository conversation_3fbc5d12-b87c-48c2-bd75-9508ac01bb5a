<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Header Settings</h1>
                    <p class="text-muted">Configure sticky header behavior for different device types and orientations</p>
                </div>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>/admin">Admin</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo BASE_URL; ?>/admin/settings">Settings</a></li>
                        <li class="breadcrumb-item active">Header Settings</li>
                    </ol>
                </nav>
            </div>

            <!-- Success Message -->
            <?php if (isset($_SESSION['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Header Settings Form -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-window-maximize me-2"></i>
                        Sticky Header Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/stickyHeaderSettings">
                        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
                        
                        <div class="row g-4">
                            <!-- Desktop Settings -->
                            <div class="col-12 col-lg-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary mb-3">
                                            <i class="fas fa-desktop me-2"></i>
                                            Desktop Devices
                                        </h6>
                                        <p class="text-muted small mb-3">Settings for desktop computers and laptops (screens 992px and larger)</p>
                                        
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="sticky_header_desktop" 
                                                   name="sticky_header_desktop" value="1" 
                                                   <?php echo $data['settings']['sticky_header_desktop'] == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sticky_header_desktop">
                                                <strong>Enable Sticky Header</strong>
                                                <br><small class="text-muted">Header stays fixed at top when scrolling</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- iPad Settings -->
                            <div class="col-12 col-lg-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-info mb-3">
                                            <i class="fab fa-apple me-2"></i>
                                            iPad Devices
                                        </h6>
                                        <p class="text-muted small mb-3">Settings for iPad tablets in different orientations</p>
                                        
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="sticky_header_ipad_portrait" 
                                                   name="sticky_header_ipad_portrait" value="1" 
                                                   <?php echo $data['settings']['sticky_header_ipad_portrait'] == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sticky_header_ipad_portrait">
                                                <strong>Portrait Mode</strong>
                                                <br><small class="text-muted">768px width - Uses PWA mobile navigation</small>
                                            </label>
                                        </div>
                                        
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="sticky_header_ipad_landscape" 
                                                   name="sticky_header_ipad_landscape" value="1" 
                                                   <?php echo $data['settings']['sticky_header_ipad_landscape'] == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sticky_header_ipad_landscape">
                                                <strong>Landscape Mode</strong>
                                                <br><small class="text-muted">1024px width - Uses desktop navigation</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Android Settings -->
                            <div class="col-12 col-lg-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-success mb-3">
                                            <i class="fab fa-android me-2"></i>
                                            Android Devices
                                        </h6>
                                        <p class="text-muted small mb-3">Settings for Android phones and tablets</p>
                                        
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="sticky_header_android_portrait" 
                                                   name="sticky_header_android_portrait" value="1" 
                                                   <?php echo $data['settings']['sticky_header_android_portrait'] == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sticky_header_android_portrait">
                                                <strong>Portrait Mode</strong>
                                                <br><small class="text-muted">Uses PWA mobile navigation</small>
                                            </label>
                                        </div>
                                        
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="sticky_header_android_landscape" 
                                                   name="sticky_header_android_landscape" value="1" 
                                                   <?php echo $data['settings']['sticky_header_android_landscape'] == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sticky_header_android_landscape">
                                                <strong>Landscape Mode</strong>
                                                <br><small class="text-muted">May use desktop or mobile navigation</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- iPhone Settings -->
                            <div class="col-12 col-lg-6">
                                <div class="card h-100 border-0 bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title text-secondary mb-3">
                                            <i class="fab fa-apple me-2"></i>
                                            iPhone Devices
                                        </h6>
                                        <p class="text-muted small mb-3">Settings for iPhone mobile devices</p>
                                        
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="sticky_header_iphone_portrait" 
                                                   name="sticky_header_iphone_portrait" value="1" 
                                                   <?php echo $data['settings']['sticky_header_iphone_portrait'] == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sticky_header_iphone_portrait">
                                                <strong>Portrait Mode</strong>
                                                <br><small class="text-muted">Uses PWA mobile navigation</small>
                                            </label>
                                        </div>
                                        
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="sticky_header_iphone_landscape" 
                                                   name="sticky_header_iphone_landscape" value="1" 
                                                   <?php echo $data['settings']['sticky_header_iphone_landscape'] == '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="sticky_header_iphone_landscape">
                                                <strong>Landscape Mode</strong>
                                                <br><small class="text-muted">Uses PWA mobile navigation</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Information Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info border-0">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-info-circle me-2"></i>
                                        How Sticky Headers Work
                                    </h6>
                                    <ul class="mb-0 small">
                                        <li><strong>Enabled:</strong> Header stays fixed at the top of the screen when scrolling</li>
                                        <li><strong>Disabled:</strong> Header scrolls with the page content (normal behavior)</li>
                                        <li><strong>PWA Mobile Navigation:</strong> Uses bottom navigation bar instead of header navigation</li>
                                        <li><strong>Desktop Navigation:</strong> Uses full header navigation menu</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Header Settings
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add visual feedback for form changes
document.addEventListener('DOMContentLoaded', function() {
    const switches = document.querySelectorAll('.form-check-input');
    
    switches.forEach(switch => {
        switch.addEventListener('change', function() {
            // Add visual feedback that settings have changed
            const saveBtn = document.querySelector('button[type="submit"]');
            if (saveBtn) {
                saveBtn.classList.add('btn-warning');
                saveBtn.classList.remove('btn-primary');
                saveBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Save Changes';
            }
        });
    });
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
