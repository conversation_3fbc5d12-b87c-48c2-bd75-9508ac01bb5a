/**
 * Android Device Compatibility JavaScript
 * Enhanced support for Android devices and Chrome mobile
 * Version: 1.0.0
 */

(function() {
    'use strict';
    
    // Detect Android devices
    const isAndroidDevice = /Android/.test(navigator.userAgent);
    const isChromeMobile = /Chrome/.test(navigator.userAgent) && /Mobile/.test(navigator.userAgent);
    
    console.log('🤖 Android Device Compatibility: Initializing...');
    console.log('🤖 Is Android Device:', isAndroidDevice);
    console.log('🤖 Is Chrome Mobile:', isChromeMobile);
    console.log('🤖 Screen dimensions:', window.innerWidth + 'x' + window.innerHeight);
    console.log('🤖 Device pixel ratio:', window.devicePixelRatio);
    
    // Force mobile mode IMMEDIATELY for Android devices to prevent login issues
    if (isAndroidDevice) {
        // Add classes immediately to prevent desktop mode from activating
        document.documentElement.classList.add('android-device', 'force-mobile-mode');

        // Force mobile navigation immediately
        const style = document.createElement('style');
        style.textContent = `
            .android-device .racing-header .navbar-collapse { display: none !important; }
            .android-device .racing-header .d-lg-flex { display: none !important; }
            .android-device body { padding-bottom: 80px !important; }
        `;
        document.head.appendChild(style);
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAndroidCompatibility);
    } else {
        initAndroidCompatibility();
    }
    
    function initAndroidCompatibility() {
        if (!isAndroidDevice) {
            console.log('🤖 Not an Android device, skipping Android-specific enhancements');
            return;
        }

        console.log('🤖 Initializing Android device enhancements...');

        // Add Android device class to body
        document.body.classList.add('android-device');
        if (isChromeMobile) document.body.classList.add('chrome-mobile');

        // Immediately force mobile navigation to prevent login lockup
        forceAndroidMobileNavigation();

        // Setup navigation monitoring
        setupAndroidNavigationFix();

        console.log('🤖 Android device enhancements initialized successfully');
    }
    
    /**
     * Setup Android navigation fix - force PWA mobile nav for all Android devices
     */
    function setupAndroidNavigationFix() {
        console.log('🤖 Setting up Android navigation fix (force PWA mode)...');

        function checkAndFixNavigation() {
            const isPortrait = window.innerHeight > window.innerWidth;
            const screenWidth = window.innerWidth;

            console.log('🤖 Android navigation check:', {
                orientation: isPortrait ? 'Portrait' : 'Landscape',
                screenWidth: screenWidth,
                shouldUseMobile: 'ALWAYS (hide desktop nav, allow FAB in landscape)'
            });

            // ALWAYS hide desktop navigation but allow FAB menu in landscape
            forceAndroidMobileNavigation(isPortrait);
        }

        // Check on load
        checkAndFixNavigation();

        // Check on orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(checkAndFixNavigation, 200);
        });

        // Check on resize
        window.addEventListener('resize', () => {
            setTimeout(checkAndFixNavigation, 100);
        });

        // Check on window load (catches login redirects)
        window.addEventListener('load', () => {
            setTimeout(checkAndFixNavigation, 50);
        });
    }

    /**
     * Force mobile navigation for Android devices
     */
    function forceAndroidMobileNavigation(isPortrait = true) {
        console.log('🤖 Forcing mobile navigation for Android...', isPortrait ? 'Portrait' : 'Landscape');

        // Hide ALL desktop navigation elements
        const desktopNav = document.querySelector('.racing-header .navbar-collapse');
        if (desktopNav) {
            desktopNav.style.display = 'none';
            desktopNav.classList.add('d-none');
        }

        // Keep menu button visible but make it work with PWA navigation
        const menuBtn = document.querySelector('.racing-menu-btn');
        if (menuBtn) {
            // Menu button stays visible for mobile navigation
            menuBtn.style.display = 'block';
        }

        // Show PWA mobile navigation
        const mobileNav = document.querySelector('.pwa-bottom-nav');
        if (mobileNav) {
            mobileNav.style.display = 'flex';
            mobileNav.classList.remove('d-none');
        }

        // Add bottom padding to body for mobile nav
        document.body.style.paddingBottom = '80px';

        // Add class to body for styling
        document.body.classList.add('android-mobile-mode');
        document.body.classList.remove('android-desktop-mode');

        // Ensure FAB menu is available in landscape mode
        if (!isPortrait) {
            setTimeout(() => {
                const fab = document.querySelector('.quick-actions-fab');
                if (fab) {
                    fab.style.display = 'block';
                    console.log('🤖 FAB menu enabled for Android landscape');
                } else {
                    // Try to trigger FAB creation
                    if (typeof addQuickActionsFAB === 'function') {
                        addQuickActionsFAB();
                        console.log('🤖 FAB menu created for Android landscape');
                    }
                }
            }, 100);
        }

        console.log('🤖 Mobile navigation forced for Android');
    }

})();
