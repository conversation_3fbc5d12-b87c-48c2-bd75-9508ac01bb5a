/**
 * Events and Shows Management System
 * Custom CSS Styles
 */

/* General Styles */
body {
    font-family: 'Roboto', sans-serif;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
}

/* Header & Navigation */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 700;
}

/* Cards */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Buttons */
.btn {
    border-radius: 4px;
    padding: 0.5rem 1.5rem;
    cursor: pointer;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Judge <PERSON><PERSON> Fix */
.judge-btn {
    cursor: pointer;
    z-index: 100;
    position: relative;
}

/* Forms */
.form-control {
    border-radius: 4px;
    padding: 0.75rem 1rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Vehicle Cards */
.vehicle-card {
    height: 100%;
}

.vehicle-card .card-img-top {
    height: 200px;
    object-fit: cover;
}

/* Show Cards */
.show-card {
    height: 100%;
}

.show-card .badge {
    font-size: 0.8rem;
}

/* Registration Status */
.status-pending {
    color: #ffc107;
}

.status-approved {
    color: #28a745;
}

.status-rejected {
    color: #dc3545;
}

.status-cancelled {
    color: #6c757d;
}

/* Payment Status */
.payment-unpaid {
    color: #dc3545;
}

.payment-paid {
    color: #28a745;
}

.payment-refunded {
    color: #17a2b8;
}

/* Judging Form */
.judging-form .form-group {
    margin-bottom: 2rem;
}

.judging-form .score-input {
    font-size: 1.5rem;
    text-align: center;
    width: 80px;
}

/* Results Table */
.results-table th, .results-table td {
    vertical-align: middle;
}

.results-table .rank {
    font-size: 1.2rem;
    font-weight: bold;
}

.results-table .rank-1 {
    color: gold;
}

.results-table .rank-2 {
    color: silver;
}

.results-table .rank-3 {
    color: #cd7f32; /* bronze */
}

/* Vehicle Gallery */
.vehicle-gallery {
    margin-bottom: 2rem;
}

.vehicle-gallery .main-image {
    height: 400px;
    object-fit: cover;
    margin-bottom: 1rem;
    border-radius: 8px;
}

.vehicle-gallery .thumbnail {
    height: 80px;
    object-fit: cover;
    cursor: pointer;
    border-radius: 4px;
    transition: opacity 0.3s ease;
}

.vehicle-gallery .thumbnail:hover {
    opacity: 0.8;
}

/* Dashboard Stats */
.dashboard-stat {
    text-align: center;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    height: 100%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.03);
}

.dashboard-stat .stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 8px;
    line-height: 1.2;
}

.dashboard-stat .stat-value i {
    font-size: 1.5rem;
}

.dashboard-stat .stat-label {
    font-size: 1rem;
    margin-bottom: 8px;
    color: inherit;
    font-weight: 500;
}

/* Compact stat boxes with specific class */
.compact-stat {
    padding: 12px !important;
    border-radius: 6px !important;
    margin-bottom: 15px !important;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08) !important;
    border: 1px solid rgba(0,0,0,0.03) !important;
}

.compact-stat .stat-value {
    font-size: 1.6rem !important;
    margin-bottom: 6px !important;
}

.compact-stat .stat-value i {
    font-size: 1.4rem !important;
}

.compact-stat .stat-label {
    font-size: 0.95rem !important;
    margin-bottom: 6px !important;
}

/* Footer */
footer {
    margin-top: 3rem;
}

footer a {
    text-decoration: none;
}

footer a:hover {
    text-decoration: underline;
}

/* Dropdown Menu Fixes */
/* Higher z-index for dropdown menus */
.dropdown-menu {
    z-index: 1080;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.15);
}

/* Ensure dropdown menus are visible when shown */
.dropdown-menu.show {
    z-index: 9999;
    display: block;
}

/* Dropdown items styling */
.dropdown-item {
    padding: 0.5rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

.dropdown-item:hover, .dropdown-item:focus {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa;
}

.dropdown-item.active, .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #007bff;
}

/* Dropdown dividers */
.dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #e9ecef;
}

/* Dropdown headers */
.dropdown-header {
    display: block;
    padding: 0.5rem 1rem;
    margin-bottom: 0;
    font-size: 0.875rem;
    color: #6c757d;
    white-space: nowrap;
}

/* Fix for dropdowns in tables and other containers */
.table-responsive .dropdown-menu,
.card-body .dropdown-menu {
    position: absolute;
    float: none;
}

/* Specific fix for vehicle image editor dropdown menus */
.custom-dropdown .dropdown-menu {
    z-index: 9999;
}

/* Ensure the batch actions card has a lower z-index */
.batch-actions-card {
    position: relative;
    z-index: 1;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .vehicle-gallery .main-image {
        height: 300px;
    }
    
    .compact-stat {
        padding: 10px !important;
    }
    
    .compact-stat .stat-value {
        font-size: 1.5rem !important;
        margin-bottom: 5px !important;
    }
    
    .compact-stat .stat-label {
        font-size: 0.9rem !important;
        margin-bottom: 5px !important;
    }
    
    /* Judge show page mobile adjustments */
    .table-responsive {
        border: none;
        margin-bottom: 0;
    }
    
    /* Ensure buttons are properly sized on mobile */
    .btn {
        white-space: normal;
        word-wrap: break-word;
    }
    
    /* Ensure card layouts don't cause horizontal scrolling */
    .card {
        width: 100%;
        max-width: 100%;
    }
    
    /* Ensure breadcrumb doesn't overflow */
    .breadcrumb {
        flex-wrap: wrap;
    }
    
    /* Mobile dropdown menu improvements */
    .dropdown-menu {
        max-width: 90vw;
        overflow-x: hidden;
    }
    
    .dropdown-item {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }
    
    /* Improve spacing in mobile dropdown menus */
    .dropdown-divider {
        margin: 0.25rem 0;
    }
    
    /* Ensure dropdown toggle buttons are more tappable */
    .dropdown-toggle {
        padding: 0.5rem 0.75rem;
        min-height: 44px; /* Minimum touch target size */
    }
    
    /* Fix for navbar dropdowns on mobile */
    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute;
    }
    
    .navbar .dropdown-item {
        white-space: normal;
    }
}

@media (max-width: 576px) {
    .vehicle-gallery .main-image {
        height: 250px;
    }
    
    .vehicle-gallery .thumbnail {
        height: 60px;
    }
    
    .compact-stat {
        padding: 8px !important;
    }
    
    .compact-stat .stat-value {
        font-size: 1.3rem !important;
        margin-bottom: 4px !important;
    }
    
    .compact-stat .stat-label {
        font-size: 0.85rem !important;
        margin-bottom: 4px !important;
    }
    
    .compact-stat .btn {
        font-size: 0.75rem !important;
        padding: 0.15rem 0.4rem !important;
    }
    
    /* Judge show page mobile adjustments */
    h1 {
        font-size: 1.8rem;
    }
    
    .card-header h5 {
        font-size: 1.1rem;
    }
    
    /* Ensure long text doesn't cause horizontal scrolling */
    p, .card-text, .list-group-item {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    /* Ensure buttons are properly sized on small mobile */
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.9rem;
    }
}

/* ===== COMPACT CLUB DISPLAY STYLES ===== */
.event-clubs-banner {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.event-clubs-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.1) 75%, transparent 75%, transparent);
    background-size: 20px 20px;
    opacity: 0.3;
}

.event-clubs-banner:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 123, 255, 0.4);
}

.club-badge {
    background: rgba(255, 255, 255, 0.95) !important;
    color: #007bff !important;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    font-weight: 600 !important;
    text-shadow: none;
    backdrop-filter: blur(10px);
}

.club-badge:hover {
    background: #ffffff !important;
    transform: scale(1.05);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.club-badge i {
    color: #007bff;
    text-shadow: none;
}

/* Animation for club badges */
@keyframes clubBadgeSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.club-badge {
    animation: clubBadgeSlideIn 0.5s ease forwards;
}

.club-badge:nth-child(1) { animation-delay: 0.1s; }
.club-badge:nth-child(2) { animation-delay: 0.2s; }
.club-badge:nth-child(3) { animation-delay: 0.3s; }
.club-badge:nth-child(4) { animation-delay: 0.4s; }
.club-badge:nth-child(5) { animation-delay: 0.5s; }
.club-badge:nth-child(6) { animation-delay: 0.6s; }

/* Responsive adjustments for compact club display */
@media (max-width: 768px) {
    .event-clubs-banner {
        padding: 1rem !important;
    }
    
    .event-clubs-banner h5 {
        font-size: 1.1rem;
    }
    
    .club-badge {
        font-size: 0.85rem !important;
        padding: 0.4rem 0.8rem !important;
    }
    
    .event-clubs-banner .fas.fa-users {
        font-size: 1.5rem !important;
    }
}

@media (max-width: 576px) {
    .event-clubs-banner {
        padding: 0.75rem !important;
    }
    
    .event-clubs-banner .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .event-clubs-banner .me-3 {
        margin-right: 0 !important;
        margin-bottom: 0.75rem;
    }
    
    .club-badge {
        margin-bottom: 0.5rem;
        display: inline-block;
    }
}