# Header Settings Feature

## Overview

The Header Settings feature allows administrators to control sticky header behavior for different device types and orientations through an easy-to-use admin interface.

## Features

### Device-Specific Controls
- **Desktop** - Screens 992px and larger
- **iPad Portrait** - 768px width, uses PWA mobile navigation
- **iPad Landscape** - 1024px width, uses desktop navigation
- **Android Portrait** - Mobile devices, uses PWA mobile navigation
- **Android Landscape** - Mobile devices in landscape
- **iPhone Portrait** - Mobile devices, uses PWA mobile navigation
- **iPhone Landscape** - Mobile devices in landscape

### Sticky Header Behavior
- **Enabled**: Header stays fixed at top when scrolling
- **Disabled**: <PERSON><PERSON> scrolls with page content (normal behavior)

## Files Added

### Admin Interface
- **`views/admin/headerSettings.php`** - Admin settings page
- **`controllers/AdminController.php`** - Added `headerSettings()` method
- **`views/admin/settings.php`** - Added Header Settings card

### Frontend Implementation
- **`public/js/header-settings.js`** - Device detection and header control
- **`api/header-settings.php`** - API endpoint for settings
- **`public/css/apple-device-compatibility.css`** - Added sticky header CSS

## How It Works

### 1. Admin Configuration
1. Navigate to `/admin/settings`
2. Click on "Header Settings" card
3. Configure sticky header for each device type
4. Save settings

### 2. Frontend Application
1. JavaScript detects device type and orientation
2. Loads settings from API endpoint
3. Applies appropriate sticky header behavior
4. Responds to orientation changes

### 3. Device Detection
```javascript
const isDesktop = window.innerWidth >= 992;
const isIPad = /iPad/.test(navigator.userAgent) || 
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
const isAndroid = /Android/.test(navigator.userAgent);
const isIPhone = /iPhone/.test(navigator.userAgent);
const isPortrait = window.innerHeight > window.innerWidth;
```

## Default Settings

- **Desktop**: Sticky enabled (traditional desktop behavior)
- **iPad Portrait**: Sticky disabled (uses PWA mobile navigation)
- **iPad Landscape**: Sticky enabled (uses desktop navigation)
- **Android Portrait**: Sticky disabled (uses PWA mobile navigation)
- **Android Landscape**: Sticky enabled (better for landscape viewing)
- **iPhone Portrait**: Sticky disabled (uses PWA mobile navigation)
- **iPhone Landscape**: Sticky enabled (better for landscape viewing)

## API Endpoint

### GET `/api/header-settings.php`

Returns current header settings:

```json
{
    "success": true,
    "settings": {
        "sticky_header_desktop": "1",
        "sticky_header_ipad_portrait": "0",
        "sticky_header_ipad_landscape": "1",
        "sticky_header_android_portrait": "0",
        "sticky_header_android_landscape": "1",
        "sticky_header_iphone_portrait": "0",
        "sticky_header_iphone_landscape": "1"
    },
    "timestamp": 1640995200
}
```

## Database Storage

Settings are stored in the `system_settings` table with the group `header`:

- `sticky_header_desktop`
- `sticky_header_ipad_portrait`
- `sticky_header_ipad_landscape`
- `sticky_header_android_portrait`
- `sticky_header_android_landscape`
- `sticky_header_iphone_portrait`
- `sticky_header_iphone_landscape`

## JavaScript Functions

### Global Functions Available

```javascript
// Manually refresh header settings
window.refreshHeaderSettings();

// Get current header settings (debugging)
window.getHeaderSettings();
```

### Debug Information

The system logs detailed information to the console:

```
📱 Header Settings: Device Detection {isDesktop: false, isIPad: true, ...}
📱 Header Settings: Loaded from server {sticky_header_desktop: "1", ...}
📱 Header Settings: Applying {deviceType: "ipad", orientation: "portrait", ...}
📱 Header Settings: Sticky header enabled {headerHeight: 80}
```

## CSS Classes

### Applied Classes
- `.header-sticky` - Added to header when sticky is enabled
- `.has-sticky-header` - Added to body when sticky header is active

### CSS Properties
```css
.header-sticky {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1030 !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}
```

## Integration with Existing Systems

### Apple Device Compatibility
- Works seamlessly with existing iPad navigation switching
- Respects PWA mobile navigation in portrait mode
- Maintains desktop navigation in landscape mode

### PWA Features
- Compatible with PWA bottom navigation
- Handles safe area insets for iOS devices
- Responds to orientation changes

## Testing

### Test Scenarios
1. **Desktop**: Verify sticky header works on large screens
2. **iPad Portrait**: Verify PWA navigation, no sticky header
3. **iPad Landscape**: Verify desktop navigation with sticky header
4. **Mobile Portrait**: Verify PWA navigation, configurable sticky
5. **Mobile Landscape**: Verify configurable sticky behavior
6. **Orientation Changes**: Verify settings apply after rotation

### Debug Commands
```javascript
// Check current settings
console.log(window.getHeaderSettings());

// Force refresh
window.refreshHeaderSettings();

// Check device detection
console.log({
    width: window.innerWidth,
    height: window.innerHeight,
    userAgent: navigator.userAgent,
    platform: navigator.platform
});
```

## Troubleshooting

### Common Issues
1. **Settings not applying**: Check API endpoint accessibility
2. **Wrong device detection**: Verify user agent strings
3. **Orientation issues**: Check window resize events
4. **CSS conflicts**: Verify z-index and positioning

### Fallback Behavior
- If API fails, uses default settings
- If device detection fails, defaults to non-sticky
- Graceful degradation for unsupported browsers

## Version Information

- **Version**: 1.0.0
- **Date**: January 2025
- **Compatibility**: All modern browsers
- **Dependencies**: Existing SettingsModel, Apple Device Compatibility

## Future Enhancements

1. **Animation Settings**: Configure header slide/fade animations
2. **Breakpoint Customization**: Custom screen size breakpoints
3. **Per-Page Settings**: Different settings for different pages
4. **User Preferences**: Allow users to override admin settings
