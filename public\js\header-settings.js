/**
 * Header Settings JavaScript
 * Applies sticky header behavior based on admin settings and device detection
 * Version: 1.0.0
 */

(function() {
    'use strict';
    
    /**
     * Dynamic device detection - called each time we need to check
     */
    function getDeviceInfo() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        const isPortrait = screenHeight > screenWidth;
        const userAgent = navigator.userAgent;
        const platform = navigator.platform;

        // PWA detection
        const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                      window.navigator.standalone === true ||
                      document.referrer.includes('android-app://');

        // Desktop detection (screens 992px and larger)
        const isDesktop = screenWidth >= 992;

        // Apple device detection
        const isIPhone = /iPhone/.test(userAgent);
        const isIPad = /iPad/.test(userAgent) ||
                       (platform === 'MacIntel' && navigator.maxTouchPoints > 1);
        const isAppleDevice = isIPhone || isIPad;

        // Android detection
        const isAndroid = /Android/.test(userAgent) && !isAppleDevice;

        // More specific device categorization
        let deviceType = 'unknown';
        if (isDesktop && !isAppleDevice && !isAndroid) {
            deviceType = 'desktop';
        } else if (isIPad) {
            deviceType = 'ipad';
        } else if (isIPhone) {
            deviceType = 'iphone';
        } else if (isAndroid) {
            deviceType = 'android';
        } else if (isDesktop) {
            deviceType = 'desktop'; // Large screen, unknown device
        }

        return {
            isDesktop,
            isIPad,
            isAndroid,
            isIPhone,
            isAppleDevice,
            isPortrait,
            isPWA,
            deviceType,
            screenWidth,
            screenHeight,
            userAgent,
            platform
        };
    }
    
    // Default settings (fallback if controller fails)
    let headerSettings = {
        sticky_header_desktop: '1',
        sticky_header_ipad_portrait: '0',
        sticky_header_ipad_landscape: '1',
        sticky_header_android_portrait: '0',
        sticky_header_android_landscape: '1',
        sticky_header_iphone: '0'
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initHeaderSettings);
    } else {
        initHeaderSettings();
    }
    
    function initHeaderSettings() {
        console.log('📱 Header Settings: Initializing...');

        // Create debug overlay for mobile testing
        createDebugOverlay();

        // Load settings from server
        loadHeaderSettings().then(() => {
            applyHeaderSettings();
            setupOrientationHandling();
        }).catch(error => {
            console.warn('📱 Header Settings: Failed to load from server, using defaults', error);
            applyHeaderSettings();
            setupOrientationHandling();
        });
    }
    
    /**
     * Load header settings from server via controller
     */
    async function loadHeaderSettings() {
        try {
            const baseUrl = window.BASE_URL || window.location.origin;
            const response = await fetch(`${baseUrl}/admin/getHeaderSettings`);

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.settings) {
                    headerSettings = { ...headerSettings, ...data.settings };
                    console.log('📱 Header Settings: Loaded from controller', headerSettings);
                }
            }
        } catch (error) {
            console.warn('📱 Header Settings: Controller not available, using defaults');
            throw error;
        }
    }
    
    /**
     * Apply header settings based on current device and orientation
     */
    function applyHeaderSettings() {
        const device = getDeviceInfo();
        const currentSetting = getCurrentHeaderSetting();
        const shouldBeSticky = currentSetting === '1';

        const debugInfo = {
            deviceType: device.deviceType,
            isPortrait: device.isPortrait,
            isPWA: device.isPWA,
            screenSize: `${device.screenWidth}x${device.screenHeight}`,
            settingKey: getStickyHeaderSettingKey(device),
            setting: currentSetting,
            shouldBeSticky
        };

        console.log('📱 Header Settings: Device Detection', debugInfo);
        console.log('📱 Header Settings: Applying', debugInfo);

        // Update debug overlay
        updateDebugOverlay(debugInfo);
        
        const header = document.querySelector('.racing-header');
        if (!header) {
            console.warn('📱 Header Settings: Header element not found');
            return;
        }

        // Force apply the setting
        if (shouldBeSticky) {
            enableStickyHeader(header);
        } else {
            disableStickyHeader(header);
            // Extra force disable for mobile devices
            if (device.deviceType !== 'desktop') {
                setTimeout(() => {
                    disableStickyHeader(header);
                }, 100);
            }
        }
    }
    
    /**
     * Get the current header setting based on device and orientation
     */
    function getCurrentHeaderSetting() {
        const device = getDeviceInfo();

        // Priority: Device type first, then screen size
        if (device.isIPhone) {
            // iPhone always uses iPhone setting regardless of orientation
            return headerSettings.sticky_header_iphone;
        } else if (device.isIPad) {
            // iPad uses orientation-specific settings
            return device.isPortrait ?
                headerSettings.sticky_header_ipad_portrait :
                headerSettings.sticky_header_ipad_landscape;
        } else if (device.isAndroid) {
            // Android uses orientation-specific settings
            return device.isPortrait ?
                headerSettings.sticky_header_android_portrait :
                headerSettings.sticky_header_android_landscape;
        } else if (device.isDesktop) {
            // Desktop/large screens use desktop setting
            return headerSettings.sticky_header_desktop;
        }

        // Default fallback - disable sticky for unknown devices
        return '0';
    }
    
    /**
     * Get device type string for logging
     */
    function getDeviceType() {
        const device = getDeviceInfo();
        return device.deviceType;
    }

    /**
     * Get the setting key being used for current device
     */
    function getStickyHeaderSettingKey(device) {
        if (device.isIPhone) {
            return 'sticky_header_iphone';
        } else if (device.isIPad) {
            return device.isPortrait ? 'sticky_header_ipad_portrait' : 'sticky_header_ipad_landscape';
        } else if (device.isAndroid) {
            return device.isPortrait ? 'sticky_header_android_portrait' : 'sticky_header_android_landscape';
        } else if (device.isDesktop) {
            return 'sticky_header_desktop';
        }
        return 'unknown';
    }
    
    /**
     * Enable sticky header
     */
    function enableStickyHeader(header) {
        header.style.position = 'fixed';
        header.style.top = '0';
        header.style.left = '0';
        header.style.right = '0';
        header.style.zIndex = '1030';
        
        // Add padding to body to compensate for fixed header
        const headerHeight = header.offsetHeight;
        document.body.style.paddingTop = `${headerHeight}px`;
        
        // Add sticky class for CSS styling
        header.classList.add('header-sticky');
        document.body.classList.add('has-sticky-header');
        
        console.log('📱 Header Settings: Sticky header enabled', { headerHeight });
    }
    
    /**
     * Disable sticky header
     */
    function disableStickyHeader(header) {
        // Force remove all sticky styles
        header.style.position = 'static';
        header.style.top = 'auto';
        header.style.left = 'auto';
        header.style.right = 'auto';
        header.style.zIndex = 'auto';
        header.style.transform = 'none';

        // Remove padding from body
        document.body.style.paddingTop = '0px';

        // Remove sticky classes
        header.classList.remove('header-sticky');
        document.body.classList.remove('has-sticky-header');

        // Force override any CSS that might be making it sticky
        header.style.cssText += '; position: static !important; top: auto !important;';

        console.log('📱 Header Settings: Sticky header disabled (forced)');
    }
    
    /**
     * Setup orientation change handling
     */
    function setupOrientationHandling() {
        let orientationTimeout;
        
        function handleOrientationChange() {
            // Clear any existing timeout
            if (orientationTimeout) {
                clearTimeout(orientationTimeout);
            }
            
            // Apply settings after orientation change completes
            orientationTimeout = setTimeout(() => {
                console.log('📱 Header Settings: Orientation changed, reapplying settings');
                applyHeaderSettings();
            }, 200);
        }
        
        // Listen for orientation changes
        window.addEventListener('orientationchange', handleOrientationChange);
        window.addEventListener('resize', handleOrientationChange);
        
        console.log('📱 Header Settings: Orientation handling setup');
    }
    
    /**
     * Global function to refresh header settings
     */
    window.refreshHeaderSettings = function() {
        console.log('📱 Header Settings: Manual refresh requested');
        loadHeaderSettings().then(() => {
            applyHeaderSettings();
        }).catch(() => {
            applyHeaderSettings();
        });
    };

    /**
     * Global function to test current device detection
     */
    window.testDeviceDetection = function() {
        const device = getDeviceInfo();
        const currentSetting = getCurrentHeaderSetting();
        const settingKey = getStickyHeaderSettingKey(device);
        console.log('📱 Device Detection Test:', {
            device,
            settingKey,
            currentSetting,
            shouldBeSticky: currentSetting === '1',
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            maxTouchPoints: navigator.maxTouchPoints
        });
        return device;
    };

    /**
     * Global function to force apply header settings (for testing)
     */
    window.forceApplyHeaderSettings = function() {
        console.log('📱 Force applying header settings...');
        applyHeaderSettings();
        return window.getHeaderSettings();
    };

    /**
     * Global function to disable sticky header (for testing)
     */
    window.disableStickyHeader = function() {
        console.log('📱 Force disabling sticky header...');
        const header = document.querySelector('.racing-header');
        if (header) {
            disableStickyHeader(header);
            console.log('📱 Sticky header disabled');
        } else {
            console.log('📱 Header element not found');
        }
    };

    /**
     * Global function to enable sticky header (for testing)
     */
    window.enableStickyHeader = function() {
        console.log('📱 Force enabling sticky header...');
        const header = document.querySelector('.racing-header');
        if (header) {
            enableStickyHeader(header);
            console.log('📱 Sticky header enabled');
        } else {
            console.log('📱 Header element not found');
        }
    };
    
    /**
     * Global function to get current header settings (for debugging)
     */
    window.getHeaderSettings = function() {
        const device = getDeviceInfo();
        return {
            settings: headerSettings,
            current: getCurrentHeaderSetting(),
            device: getDeviceType(),
            deviceInfo: device,
            orientation: device.isPortrait ? 'portrait' : 'landscape',
            isSticky: document.querySelector('.racing-header')?.classList.contains('header-sticky')
        };
    };

    /**
     * Create debug overlay for mobile testing
     */
    function createDebugOverlay() {
        // Only create if it doesn't exist
        if (document.getElementById('header-debug-overlay')) return;

        const overlay = document.createElement('div');
        overlay.id = 'header-debug-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
            max-width: 300px;
            display: none;
        `;

        // Add toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.innerHTML = '🐛';
        toggleBtn.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 16px;
            z-index: 10000;
            cursor: pointer;
        `;

        toggleBtn.onclick = function() {
            const isVisible = overlay.style.display !== 'none';
            overlay.style.display = isVisible ? 'none' : 'block';
            toggleBtn.style.right = isVisible ? '10px' : '320px';
        };

        document.body.appendChild(overlay);
        document.body.appendChild(toggleBtn);
    }

    /**
     * Update debug overlay with current info
     */
    function updateDebugOverlay(debugInfo) {
        const overlay = document.getElementById('header-debug-overlay');
        if (!overlay) return;

        const header = document.querySelector('.racing-header');
        const isActuallySticky = header?.classList.contains('header-sticky');

        overlay.innerHTML = `
            <strong>Header Debug Info</strong><br>
            Device: ${debugInfo.deviceType}<br>
            Screen: ${debugInfo.screenSize}<br>
            Orientation: ${debugInfo.isPortrait ? 'Portrait' : 'Landscape'}<br>
            PWA: ${debugInfo.isPWA ? 'Yes' : 'No'}<br>
            Setting Key: ${debugInfo.settingKey}<br>
            Setting Value: ${debugInfo.setting}<br>
            Should Be Sticky: ${debugInfo.shouldBeSticky ? 'Yes' : 'No'}<br>
            Actually Sticky: ${isActuallySticky ? 'Yes' : 'No'}<br>
            <br>
            <button onclick="window.forceApplyHeaderSettings()" style="background:#28a745;color:white;border:none;padding:5px;border-radius:3px;cursor:pointer;margin:2px;">Reapply</button>
            <button onclick="window.disableStickyHeader()" style="background:#dc3545;color:white;border:none;padding:5px;border-radius:3px;cursor:pointer;margin:2px;">Force Off</button><br>
            <button onclick="window.refreshHeaderSettings()" style="background:#ffc107;color:black;border:none;padding:5px;border-radius:3px;cursor:pointer;margin:2px;">Reload Settings</button>
            <button onclick="window.forceDisableAll()" style="background:#6c757d;color:white;border:none;padding:5px;border-radius:3px;cursor:pointer;margin:2px;">Disable All</button>
        `;
    }

    /**
     * Global function to toggle debug overlay
     */
    window.toggleHeaderDebug = function() {
        const overlay = document.getElementById('header-debug-overlay');
        if (overlay) {
            const isVisible = overlay.style.display !== 'none';
            overlay.style.display = isVisible ? 'none' : 'block';
        }
    };

    /**
     * Global function to force disable all sticky headers
     */
    window.forceDisableAll = function() {
        console.log('📱 Force disabling ALL sticky headers...');

        // Override all settings to disabled
        headerSettings = {
            sticky_header_desktop: '0',
            sticky_header_ipad_portrait: '0',
            sticky_header_ipad_landscape: '0',
            sticky_header_android_portrait: '0',
            sticky_header_android_landscape: '0',
            sticky_header_iphone: '0'
        };

        // Force disable header
        const header = document.querySelector('.racing-header');
        if (header) {
            disableStickyHeader(header);

            // Extra aggressive disable
            setTimeout(() => {
                disableStickyHeader(header);
                header.style.cssText += '; position: static !important; top: auto !important; transform: none !important;';
            }, 100);

            setTimeout(() => {
                disableStickyHeader(header);
            }, 500);
        }

        // Update debug overlay
        const device = getDeviceInfo();
        updateDebugOverlay({
            deviceType: device.deviceType,
            isPortrait: device.isPortrait,
            isPWA: device.isPWA,
            screenSize: `${device.screenWidth}x${device.screenHeight}`,
            settingKey: getStickyHeaderSettingKey(device),
            setting: '0',
            shouldBeSticky: false
        });

        console.log('📱 All sticky headers force disabled');
    };

})();
