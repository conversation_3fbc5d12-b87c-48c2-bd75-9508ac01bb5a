/**
 * Header Settings JavaScript
 * Applies sticky header behavior based on admin settings and device detection
 * Version: 1.0.0
 */

(function() {
    'use strict';
    
    // Device detection
    const isDesktop = window.innerWidth >= 992;
    const isIPad = /iPad/.test(navigator.userAgent) || 
                   (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    const isAndroid = /Android/.test(navigator.userAgent);
    const isIPhone = /iPhone/.test(navigator.userAgent);
    const isPortrait = window.innerHeight > window.innerWidth;
    
    console.log('📱 Header Settings: Device Detection', {
        isDesktop,
        isIPad,
        isAndroid,
        isIPhone,
        isPortrait,
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight
    });
    
    // Default settings (fallback if API fails)
    let headerSettings = {
        sticky_header_desktop: '1',
        sticky_header_ipad_portrait: '0',
        sticky_header_ipad_landscape: '1',
        sticky_header_android_portrait: '0',
        sticky_header_android_landscape: '1',
        sticky_header_iphone_portrait: '0',
        sticky_header_iphone_landscape: '1'
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initHeaderSettings);
    } else {
        initHeaderSettings();
    }
    
    function initHeaderSettings() {
        console.log('📱 Header Settings: Initializing...');
        
        // Load settings from server
        loadHeaderSettings().then(() => {
            applyHeaderSettings();
            setupOrientationHandling();
        }).catch(error => {
            console.warn('📱 Header Settings: Failed to load from server, using defaults', error);
            applyHeaderSettings();
            setupOrientationHandling();
        });
    }
    
    /**
     * Load header settings from server via controller
     */
    async function loadHeaderSettings() {
        try {
            const baseUrl = window.BASE_URL || window.location.origin;
            const response = await fetch(`${baseUrl}/admin/getHeaderSettings`);

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.settings) {
                    headerSettings = { ...headerSettings, ...data.settings };
                    console.log('📱 Header Settings: Loaded from controller', headerSettings);
                }
            }
        } catch (error) {
            console.warn('📱 Header Settings: Controller not available, using defaults');
            throw error;
        }
    }
    
    /**
     * Apply header settings based on current device and orientation
     */
    function applyHeaderSettings() {
        const currentSetting = getCurrentHeaderSetting();
        const shouldBeSticky = currentSetting === '1';
        
        console.log('📱 Header Settings: Applying', {
            deviceType: getDeviceType(),
            orientation: isPortrait ? 'portrait' : 'landscape',
            setting: currentSetting,
            shouldBeSticky
        });
        
        const header = document.querySelector('.racing-header');
        if (!header) {
            console.warn('📱 Header Settings: Header element not found');
            return;
        }
        
        if (shouldBeSticky) {
            enableStickyHeader(header);
        } else {
            disableStickyHeader(header);
        }
    }
    
    /**
     * Get the current header setting based on device and orientation
     */
    function getCurrentHeaderSetting() {
        if (isDesktop) {
            return headerSettings.sticky_header_desktop;
        } else if (isIPad) {
            return isPortrait ? 
                headerSettings.sticky_header_ipad_portrait : 
                headerSettings.sticky_header_ipad_landscape;
        } else if (isAndroid) {
            return isPortrait ? 
                headerSettings.sticky_header_android_portrait : 
                headerSettings.sticky_header_android_landscape;
        } else if (isIPhone) {
            return isPortrait ? 
                headerSettings.sticky_header_iphone_portrait : 
                headerSettings.sticky_header_iphone_landscape;
        }
        
        // Default fallback
        return '0';
    }
    
    /**
     * Get device type string for logging
     */
    function getDeviceType() {
        if (isDesktop) return 'desktop';
        if (isIPad) return 'ipad';
        if (isAndroid) return 'android';
        if (isIPhone) return 'iphone';
        return 'unknown';
    }
    
    /**
     * Enable sticky header
     */
    function enableStickyHeader(header) {
        header.style.position = 'fixed';
        header.style.top = '0';
        header.style.left = '0';
        header.style.right = '0';
        header.style.zIndex = '1030';
        
        // Add padding to body to compensate for fixed header
        const headerHeight = header.offsetHeight;
        document.body.style.paddingTop = `${headerHeight}px`;
        
        // Add sticky class for CSS styling
        header.classList.add('header-sticky');
        document.body.classList.add('has-sticky-header');
        
        console.log('📱 Header Settings: Sticky header enabled', { headerHeight });
    }
    
    /**
     * Disable sticky header
     */
    function disableStickyHeader(header) {
        header.style.position = '';
        header.style.top = '';
        header.style.left = '';
        header.style.right = '';
        header.style.zIndex = '';
        
        // Remove padding from body
        document.body.style.paddingTop = '';
        
        // Remove sticky class
        header.classList.remove('header-sticky');
        document.body.classList.remove('has-sticky-header');
        
        console.log('📱 Header Settings: Sticky header disabled');
    }
    
    /**
     * Setup orientation change handling
     */
    function setupOrientationHandling() {
        let orientationTimeout;
        
        function handleOrientationChange() {
            // Clear any existing timeout
            if (orientationTimeout) {
                clearTimeout(orientationTimeout);
            }
            
            // Apply settings after orientation change completes
            orientationTimeout = setTimeout(() => {
                console.log('📱 Header Settings: Orientation changed, reapplying settings');
                applyHeaderSettings();
            }, 200);
        }
        
        // Listen for orientation changes
        window.addEventListener('orientationchange', handleOrientationChange);
        window.addEventListener('resize', handleOrientationChange);
        
        console.log('📱 Header Settings: Orientation handling setup');
    }
    
    /**
     * Global function to refresh header settings
     */
    window.refreshHeaderSettings = function() {
        console.log('📱 Header Settings: Manual refresh requested');
        loadHeaderSettings().then(() => {
            applyHeaderSettings();
        }).catch(() => {
            applyHeaderSettings();
        });
    };
    
    /**
     * Global function to get current header settings (for debugging)
     */
    window.getHeaderSettings = function() {
        return {
            settings: headerSettings,
            current: getCurrentHeaderSetting(),
            device: getDeviceType(),
            orientation: isPortrait ? 'portrait' : 'landscape',
            isSticky: document.querySelector('.racing-header')?.classList.contains('header-sticky')
        };
    };
    
})();
