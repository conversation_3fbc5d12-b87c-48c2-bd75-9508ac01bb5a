/**
 * Apple Device Compatibility JavaScript
 * Enhanced support for iOS Safari, iPhone, iPad, and other Apple devices
 * Version: 1.0.0
 */

(function() {
    'use strict';
    
    // Detect Apple devices
    const isAppleDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
                         (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    const isIOSSafari = /iPad|iPhone|iPod/.test(navigator.userAgent) && /Safari/.test(navigator.userAgent);
    const isStandalone = window.navigator.standalone === true;
    const isIPad = /iPad/.test(navigator.userAgent) ||
                   (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    
    console.log('🍎 Apple Device Compatibility: Initializing...');
    console.log('🍎 Is Apple Device:', isAppleDevice);
    console.log('🍎 Is iOS Safari:', isIOSSafari);
    console.log('🍎 Is PWA Standalone:', isStandalone);
    console.log('🍎 Is iPad:', isIPad);
    console.log('🍎 Screen dimensions:', window.innerWidth + 'x' + window.innerHeight);
    console.log('🍎 Device pixel ratio:', window.devicePixelRatio);
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAppleCompatibility);
    } else {
        initAppleCompatibility();
    }
    
    function initAppleCompatibility() {
        if (!isAppleDevice) {
            console.log('🍎 Not an Apple device, skipping Apple-specific enhancements');
            return;
        }
        
        console.log('🍎 Initializing Apple device enhancements...');
        
        // Add Apple device class to body
        document.body.classList.add('apple-device');
        if (isIOSSafari) document.body.classList.add('ios-safari');
        if (isStandalone) document.body.classList.add('ios-standalone');
        if (isIPad) document.body.classList.add('ipad-device');
        
        // Initialize all Apple-specific features
        fixIOSViewportHeight();
        enhanceIOSTouchEvents();
        fixIOSFormZoom();
        setupIOSScrollFixes();
        enhanceIOSModalHandling();
        setupIOSOrientationHandling();
        setupIOSKeyboardHandling();

        // iPad-specific fixes
        if (isIPad) {
            setupIPadNavigationLogic();
            setupIPadLayoutMonitoring();
        }
        
        console.log('🍎 Apple device enhancements initialized successfully');
    }
    
    /**
     * Fix iOS Safari viewport height issues
     */
    function fixIOSViewportHeight() {
        if (!isIOSSafari) return;
        
        function setViewportHeight() {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        }
        
        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', () => {
            setTimeout(setViewportHeight, 100);
        });
        
        console.log('🍎 iOS viewport height fix applied');
    }
    
    /**
     * Enhance touch events for iOS devices
     */
    function enhanceIOSTouchEvents() {
        // Add haptic feedback simulation for buttons
        const buttons = document.querySelectorAll('.btn, button, .racing-menu-btn, .nav-link');
        
        buttons.forEach(button => {
            button.addEventListener('touchstart', function(e) {
                this.classList.add('ios-touch-active');
                
                // Simulate haptic feedback with visual feedback
                if (this.style.transform !== 'scale(0.98)') {
                    this.style.transform = 'scale(0.98)';
                    this.style.transition = 'transform 0.1s ease';
                }
            }, { passive: true });
            
            button.addEventListener('touchend', function(e) {
                this.classList.remove('ios-touch-active');
                
                setTimeout(() => {
                    this.style.transform = '';
                }, 100);
            }, { passive: true });
            
            button.addEventListener('touchcancel', function(e) {
                this.classList.remove('ios-touch-active');
                this.style.transform = '';
            }, { passive: true });
        });
        
        console.log('🍎 iOS touch events enhanced for', buttons.length, 'elements');
    }
    
    /**
     * Fix iOS Safari form input zoom
     */
    function fixIOSFormZoom() {
        if (!isIOSSafari) return;
        
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"], input[type="tel"], input[type="url"], input[type="search"], textarea, select');
        
        inputs.forEach(input => {
            // Ensure font-size is at least 16px to prevent zoom
            const computedStyle = window.getComputedStyle(input);
            const fontSize = parseFloat(computedStyle.fontSize);
            
            if (fontSize < 16) {
                input.style.fontSize = '16px';
            }
        });
        
        console.log('🍎 iOS form zoom prevention applied to', inputs.length, 'inputs');
    }
    
    /**
     * Setup iOS scroll fixes
     */
    function setupIOSScrollFixes() {
        if (!isIOSSafari) return;
        
        // Fix momentum scrolling
        document.body.style.webkitOverflowScrolling = 'touch';
        
        // Fix scroll position after orientation change
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                window.scrollTo(0, window.pageYOffset);
            }, 100);
        });
        
        console.log('🍎 iOS scroll fixes applied');
    }
    
    /**
     * Enhance modal handling for iOS
     */
    function enhanceIOSModalHandling() {
        if (!isIOSSafari) return;
        
        // Monitor for modal creation
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1 && node.classList && node.classList.contains('modal')) {
                        enhanceModalForIOS(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Enhance existing modals
        document.querySelectorAll('.modal').forEach(enhanceModalForIOS);
        
        function enhanceModalForIOS(modal) {
            // Fix iOS modal backdrop issues
            modal.style.webkitTransform = 'translateZ(0)';
            modal.style.transform = 'translateZ(0)';
            
            const modalDialog = modal.querySelector('.modal-dialog');
            if (modalDialog) {
                modalDialog.style.webkitTransform = 'translateZ(0)';
                modalDialog.style.transform = 'translateZ(0)';
            }
            
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
                modalContent.style.webkitTransform = 'translateZ(0)';
                modalContent.style.transform = 'translateZ(0)';
                modalContent.style.webkitBackfaceVisibility = 'hidden';
                modalContent.style.backfaceVisibility = 'hidden';
            }
            
            // Fix iOS modal scrolling
            modal.style.webkitOverflowScrolling = 'touch';
        }
        
        console.log('🍎 iOS modal enhancements applied');
    }
    
    /**
     * Setup iOS orientation change handling
     */
    function setupIOSOrientationHandling() {
        if (!isAppleDevice) return;
        
        let orientationTimeout;
        
        window.addEventListener('orientationchange', function() {
            // Clear any existing timeout
            if (orientationTimeout) {
                clearTimeout(orientationTimeout);
            }
            
            // Add orientation change class
            document.body.classList.add('orientation-changing');
            
            // Remove class and fix layout after orientation change completes
            orientationTimeout = setTimeout(() => {
                document.body.classList.remove('orientation-changing');
                
                // Trigger resize event to fix any layout issues
                window.dispatchEvent(new Event('resize'));
                
                // Fix any stuck modals
                const openModals = document.querySelectorAll('.modal.show');
                openModals.forEach(modal => {
                    modal.style.display = 'none';
                    setTimeout(() => {
                        modal.style.display = '';
                    }, 10);
                });
                
            }, 500);
        });
        
        console.log('🍎 iOS orientation change handling setup');
    }
    
    /**
     * Setup iOS keyboard handling
     */
    function setupIOSKeyboardHandling() {
        if (!isIOSSafari) return;
        
        let initialViewportHeight = window.innerHeight;
        
        // Detect virtual keyboard
        window.addEventListener('resize', function() {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;
            
            if (heightDifference > 150) {
                // Keyboard is likely open
                document.body.classList.add('ios-keyboard-open');
            } else {
                // Keyboard is likely closed
                document.body.classList.remove('ios-keyboard-open');
            }
        });
        
        // Fix scroll position when keyboard closes
        document.addEventListener('focusout', function(e) {
            if (e.target.matches('input, textarea, select')) {
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 100);
            }
        });
        
        console.log('🍎 iOS keyboard handling setup');
    }
    
    /**
     * Global function to force refresh iOS layout
     */
    window.refreshIOSLayout = function() {
        if (!isAppleDevice) return;
        
        console.log('🍎 Forcing iOS layout refresh...');
        
        // Trigger reflow
        document.body.style.display = 'none';
        document.body.offsetHeight; // Force reflow
        document.body.style.display = '';
        
        // Dispatch resize event
        window.dispatchEvent(new Event('resize'));
        
        console.log('🍎 iOS layout refresh completed');
    };
    
    /**
     * Setup iPad navigation logic - use PWA mobile nav in portrait, desktop nav in landscape
     */
    function setupIPadNavigationLogic() {
        if (!isIPad) return;

        console.log('🍎 Setting up iPad navigation logic...');

        function applyNavigationMode() {
            const isPortrait = window.innerHeight > window.innerWidth;
            const screenWidth = window.innerWidth;

            console.log('🍎 iPad navigation check:', {
                orientation: isPortrait ? 'Portrait' : 'Landscape',
                screenWidth: screenWidth,
                shouldUseMobileNav: isPortrait,
                isIPadSpecific: true
            });

            // Only apply iPad-specific logic for actual iPad devices
            if (isPortrait) {
                // Use mobile PWA navigation for iPad portrait
                activateIPadMobileNavigation();
            } else {
                // Use desktop navigation with compact layout for iPad landscape
                activateIPadDesktopNavigation();
            }
        }

        // Apply on load
        applyNavigationMode();

        // Apply on orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(applyNavigationMode, 100);
        });

        // Apply on resize
        window.addEventListener('resize', () => {
            setTimeout(applyNavigationMode, 50);
        });
    }

    /**
     * Activate mobile PWA navigation for iPad portrait (iPad-specific)
     */
    function activateIPadMobileNavigation() {
        console.log('🍎 Activating iPad-specific mobile PWA navigation...');

        // Add iPad mobile navigation mode class
        document.body.classList.add('ipad-mobile-nav-mode');
        document.body.classList.remove('ipad-desktop-nav-mode');

        // Only modify navigation if we're on iPad - don't interfere with regular mobile
        if (!isIPad) return;

        // Force mobile menu button to show and position on right
        const mobileMenuBtn = document.querySelector('.racing-menu-btn.d-lg-none');
        if (mobileMenuBtn) {
            mobileMenuBtn.style.display = 'block';
            mobileMenuBtn.style.marginLeft = 'auto';
            mobileMenuBtn.style.order = '999';
        }

        // Ensure the navbar uses flexbox properly for button positioning
        const navbar = document.querySelector('.racing-navbar');
        if (navbar) {
            navbar.style.display = 'flex';
            navbar.style.justifyContent = 'space-between';
            navbar.style.alignItems = 'center';
        }

        // Ensure PWA bottom navigation is visible (if it exists)
        let mobileNavBottom = document.querySelector('.mobile-nav-bottom');
        if (mobileNavBottom) {
            mobileNavBottom.classList.add('show');
            mobileNavBottom.style.display = 'block';
        }

        console.log('🍎 iPad mobile PWA navigation activated');
    }

    /**
     * Activate desktop navigation for iPad landscape (iPad-specific)
     */
    function activateIPadDesktopNavigation() {
        console.log('🍎 Activating iPad-specific desktop navigation...');

        // Add iPad desktop navigation mode class
        document.body.classList.add('ipad-desktop-nav-mode');
        document.body.classList.remove('ipad-mobile-nav-mode');

        // Only modify navigation if we're on iPad
        if (!isIPad) return;

        // Reset mobile menu button styles
        const mobileMenuBtn = document.querySelector('.racing-menu-btn.d-lg-none');
        if (mobileMenuBtn) {
            mobileMenuBtn.style.marginLeft = '';
            mobileMenuBtn.style.order = '';
        }

        // Reset navbar styles
        const navbar = document.querySelector('.racing-navbar');
        if (navbar) {
            navbar.style.display = '';
            navbar.style.justifyContent = '';
            navbar.style.alignItems = '';
        }

        console.log('🍎 iPad desktop navigation activated');
    }

    /**
     * Apply compact desktop layout for iPad landscape
     */
    function applyCompactDesktopLayout() {
        const header = document.querySelector('.racing-header');
        const container = header?.querySelector('.container');

        if (container) {
            container.style.maxWidth = '100%';
            container.style.paddingLeft = '15px';
            container.style.paddingRight = '15px';
        }

        // Compact navigation links
        const navLinks = document.querySelectorAll('.racing-header .racing-nav-link');
        navLinks.forEach(link => {
            link.style.padding = '0.5rem 0.6rem';
            link.style.fontSize = '0.85rem';
        });

        // Compact brand
        const brand = document.querySelector('.racing-brand');
        if (brand) {
            brand.style.fontSize = '1.3rem';
        }

        // Reduce first nav item margin
        const firstNavItem = document.querySelector('.navbar-nav .nav-item:first-child .racing-nav-link');
        if (firstNavItem) {
            firstNavItem.style.marginLeft = '1rem';
        }
    }

    /**
     * Create mobile navigation if it doesn't exist
     */
    function createMobileNavigation() {
        console.log('🍎 Creating mobile navigation...');

        // Check if it already exists
        if (document.querySelector('.mobile-nav-bottom')) {
            return;
        }

        // Create the mobile navigation element
        const mobileNav = document.createElement('div');
        mobileNav.className = 'mobile-nav-bottom show';
        mobileNav.innerHTML = `
            <div class="mobile-nav-items">
                <a href="${window.BASE_URL || ''}/" class="mobile-nav-item">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
                <a href="${window.BASE_URL || ''}/calendar" class="mobile-nav-item">
                    <i class="fas fa-calendar"></i>
                    <span>Events</span>
                </a>
                <a href="${window.BASE_URL || ''}/user/dashboard" class="mobile-nav-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="${window.BASE_URL || ''}/notification_center" class="mobile-nav-item">
                    <i class="fas fa-bell"></i>
                    <span>Messages</span>
                </a>
                <a href="#" class="mobile-nav-item" onclick="forceAppRefresh()" style="color: #1338BE;">
                    <i class="fas fa-sync-alt"></i>
                    <span>Refresh</span>
                </a>
            </div>
        `;

        document.body.appendChild(mobileNav);
        console.log('🍎 Mobile navigation created');
    }

    /**
     * Apply extreme space-saving measures for iPad portrait if needed
     */
    function checkAndApplyExtremePortraitFixes() {
        if (!isIPad || window.innerHeight <= window.innerWidth) return;

        const userDropdown = document.querySelector('.racing-header .nav-item.dropdown:last-child');
        if (!userDropdown) return;

        const rect = userDropdown.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const isOverflowing = rect.right > viewportWidth;

        console.log('🍎 Portrait overflow check:', {
            userMenuRight: rect.right,
            viewportWidth: viewportWidth,
            isOverflowing: isOverflowing,
            overflowBy: isOverflowing ? rect.right - viewportWidth : 0
        });

        if (isOverflowing) {
            console.log('🍎 Applying EXTREME portrait fixes...');

            // Hide text in navigation, keep only icons
            const navLinks = document.querySelectorAll('.racing-header .racing-nav-link');
            navLinks.forEach(link => {
                const icon = link.querySelector('i');
                const text = link.childNodes;

                // Hide text nodes, keep icons
                text.forEach(node => {
                    if (node.nodeType === 3 && node.textContent.trim()) { // Text node
                        node.textContent = '';
                    }
                });

                if (icon) {
                    link.style.padding = '0.3rem 0.1rem';
                    link.style.fontSize = '0.8rem';
                }
            });

            // Make brand even smaller
            const brand = document.querySelector('.racing-brand');
            if (brand) {
                brand.style.fontSize = '0.7rem';
                brand.style.marginRight = '0.1rem';
            }

            // Ultra-compact container
            const container = document.querySelector('.racing-header .container');
            if (container) {
                container.style.paddingLeft = '2px';
                container.style.paddingRight = '2px';
            }

            console.log('🍎 EXTREME portrait fixes applied');
        }
    }

    /**
     * Setup iPad layout monitoring
     */
    function setupIPadLayoutMonitoring() {
        if (!isIPad) return;

        // Monitor for orientation changes
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                fixIPadHeaderLayout();
                console.log('🍎 iPad layout refreshed after orientation change');
            }, 100);
        });

        // Monitor for resize events
        window.addEventListener('resize', function() {
            setTimeout(() => {
                fixIPadHeaderLayout();
            }, 50);
        });

        console.log('🍎 iPad layout monitoring setup');
    }

    /**
     * Analyze iPad layout and provide debugging info
     */
    window.analyzeIPadLayout = function() {
        if (!isIPad) {
            console.log('🍎 Not an iPad device');
            return;
        }

        console.log('🍎 === iPad Layout Analysis ===');

        const header = document.querySelector('.racing-header');
        const container = header?.querySelector('.container');
        const navbar = header?.querySelector('.navbar');
        const leftNav = document.querySelector('.racing-header .navbar-nav.me-auto');
        const rightNav = document.querySelector('.racing-header .navbar-nav:not(.me-auto)');

        if (header) {
            console.log('🍎 Header:', {
                width: header.offsetWidth,
                scrollWidth: header.scrollWidth,
                overflow: header.scrollWidth > header.offsetWidth ? 'YES' : 'NO'
            });
        }

        if (container) {
            console.log('🍎 Container:', {
                width: container.offsetWidth,
                scrollWidth: container.scrollWidth,
                paddingLeft: getComputedStyle(container).paddingLeft,
                paddingRight: getComputedStyle(container).paddingRight
            });
        }

        if (navbar) {
            console.log('🍎 Navbar:', {
                width: navbar.offsetWidth,
                scrollWidth: navbar.scrollWidth
            });
        }

        if (leftNav) {
            console.log('🍎 Left Navigation:', {
                width: leftNav.offsetWidth,
                scrollWidth: leftNav.scrollWidth,
                itemCount: leftNav.querySelectorAll('.nav-item').length
            });
        }

        if (rightNav) {
            console.log('🍎 Right Navigation:', {
                width: rightNav.offsetWidth,
                scrollWidth: rightNav.scrollWidth,
                itemCount: rightNav.querySelectorAll('.nav-item').length,
                position: getComputedStyle(rightNav).position
            });
        }

        // Check if user menu is visible
        const userDropdown = document.querySelector('.racing-header .nav-item.dropdown:last-child');
        if (userDropdown) {
            const rect = userDropdown.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            console.log('🍎 User Menu:', {
                left: rect.left,
                right: rect.right,
                width: rect.width,
                viewportWidth: viewportWidth,
                isVisible: rect.right <= viewportWidth,
                overflowBy: rect.right > viewportWidth ? rect.right - viewportWidth : 0
            });
        }

        console.log('🍎 === End Analysis ===');
    };

    /**
     * Global function to force iPad navigation refresh
     */
    window.refreshIPadLayout = function() {
        if (!isIPad) {
            console.log('🍎 Not an iPad device, skipping iPad layout refresh');
            return;
        }

        console.log('🍎 Forcing iPad navigation refresh...');
        setupIPadNavigationLogic();

        // Also trigger general iOS layout refresh
        if (window.refreshIOSLayout) {
            window.refreshIOSLayout();
        }

        // Run analysis after fixes
        setTimeout(() => {
            window.analyzeIPadLayout();
        }, 100);

        console.log('🍎 iPad navigation refresh completed');
    };

    /**
     * Export compatibility info for debugging
     */
    window.appleDeviceInfo = {
        isAppleDevice,
        isIOSSafari,
        isStandalone,
        isIPad,
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        maxTouchPoints: navigator.maxTouchPoints,
        standalone: window.navigator.standalone,
        screenWidth: window.innerWidth,
        screenHeight: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio
    };
    
})();
