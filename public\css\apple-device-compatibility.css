/**
 * Apple Device Compatibility CSS
 * Enhanced support for iOS Safari, iPhone, iPad, and other Apple devices
 * Version: 1.0.0
 */

/* iOS Safe Area Support for devices with notches (iPhone X and newer) */
:root {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
}

/* Header safe area adjustments */
.racing-header {
    padding-top: calc(1rem + var(--safe-area-inset-top));
    padding-left: calc(1rem + var(--safe-area-inset-left));
    padding-right: calc(1rem + var(--safe-area-inset-right));
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
    /* This targets iOS Safari specifically */
    
    /* Prevent iOS Safari from adding rounded corners and shadows to buttons */
    .btn, button, input[type="submit"], input[type="button"] {
        -webkit-appearance: none;
        -webkit-border-radius: 0;
        border-radius: 8px;
        background-clip: padding-box;
    }
    
    /* Fix iOS Safari input styling */
    input[type="text"], 
    input[type="email"], 
    input[type="password"], 
    input[type="tel"], 
    input[type="url"], 
    input[type="search"], 
    textarea, 
    select {
        -webkit-appearance: none;
        -webkit-border-radius: 0;
        border-radius: 4px;
        background-clip: padding-box;
    }
    
    /* Prevent iOS Safari zoom on form inputs */
    input[type="text"], 
    input[type="email"], 
    input[type="password"], 
    input[type="tel"], 
    input[type="url"], 
    input[type="search"], 
    textarea, 
    select {
        font-size: 16px !important;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }
}

/* WebKit specific optimizations */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
    /* High DPI display optimizations */
    .racing-header {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    /* Smooth scrolling for WebKit */
    html {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }
    
    /* Better text rendering on Retina displays */
    body, .racing-brand, .nav-link {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}

/* Touch action optimizations for iOS */
.racing-menu-btn, 
.btn, 
button, 
.nav-link, 
.dropdown-toggle {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* iOS specific button active states */
.racing-menu-btn:active,
.btn:active,
button:active {
    transform: scale(0.98);
    -webkit-transform: scale(0.98);
    transition: transform 0.1s ease;
    -webkit-transition: -webkit-transform 0.1s ease;
}

/* Fix iOS Safari modal backdrop issues */
.modal-backdrop {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* iOS Safari specific modal fixes */
@supports (-webkit-touch-callout: none) {
    .modal {
        -webkit-overflow-scrolling: touch;
    }
    
    .modal-dialog {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
    
    .modal-content {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
}

/* iPhone landscape orientation fixes */
@media screen and (max-width: 896px) and (orientation: landscape) {
    .racing-header {
        padding-top: calc(0.5rem + var(--safe-area-inset-top));
        min-height: 60px;
    }
    
    .racing-navbar {
        min-height: 50px;
    }
    
    .racing-brand img {
        height: 40px;
    }
}

/* iPad specific optimizations */
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .racing-header {
        padding-top: calc(1.5rem + var(--safe-area-inset-top));
    }

    /* Fix container width for iPad to prevent user menu cutoff */
    .racing-header .container {
        max-width: 100% !important;
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    /* Ensure navbar takes full width */
    .racing-header .navbar {
        width: 100%;
        padding-left: 0;
        padding-right: 0;
    }

    /* Adjust navigation spacing for iPad */
    .racing-header .navbar-nav .nav-link {
        padding-left: 0.5rem !important;
        padding-right: 0.5rem !important;
        font-size: 0.9rem;
    }

    /* Ensure user dropdown doesn't get cut off */
    .racing-header .dropdown-menu-end {
        right: 0 !important;
        left: auto !important;
        transform: none !important;
        margin-right: 0 !important;
    }

    /* Better touch targets for iPad */
    .nav-link {
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    .btn {
        min-height: 44px;
        padding: 12px 20px;
    }

    /* Reduce brand font size slightly on iPad */
    .racing-brand {
        font-size: 1.3rem !important;
    }

    /* Ensure notification bell is properly sized */
    .racing-notification {
        padding: 0.5rem !important;
    }

    /* Fix user profile image in dropdown */
    .racing-nav-link img {
        width: 32px !important;
        height: 32px !important;
    }
}

/* iPad landscape orientation fixes */
@media screen and (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
    .racing-header .container {
        max-width: 100% !important;
        padding-left: 20px !important;
        padding-right: 20px !important;
    }

    /* More compact navigation in landscape */
    .racing-header .navbar-nav .nav-link {
        padding-left: 0.4rem !important;
        padding-right: 0.4rem !important;
        font-size: 0.85rem;
    }

    .racing-brand {
        font-size: 1.2rem !important;
    }
}

/* iPad portrait should use mobile PWA navigation - no desktop nav fixes needed */

/* iOS PWA specific styles */
@media (display-mode: standalone) {
    /* When app is installed as PWA on iOS */
    body {
        padding-top: var(--safe-area-inset-top);
        padding-bottom: var(--safe-area-inset-bottom);
    }

    .racing-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1030;
        padding-top: calc(1rem + var(--safe-area-inset-top));
    }

    /* Adjust main content for fixed header */
    main, .main-content {
        margin-top: calc(80px + var(--safe-area-inset-top));
    }
}

/* iOS Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .racing-header {
        border-bottom-color: #444;
    }
    
    .racing-brand, .nav-link {
        color: #e0e0e0 !important;
    }
}

/* iOS Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .racing-menu-btn:active,
    .btn:active,
    button:active {
        transform: none;
        -webkit-transform: none;
        transition: none;
        -webkit-transition: none;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* iOS VoiceOver and accessibility improvements */
@media (prefers-reduced-motion: no-preference) {
    .racing-menu-btn:focus,
    .btn:focus,
    .nav-link:focus {
        outline: 2px solid #007AFF;
        outline-offset: 2px;
    }
}

/* Fix iOS Safari viewport height issues */
@supports (-webkit-touch-callout: none) {
    .min-vh-100 {
        min-height: -webkit-fill-available;
    }
    
    html {
        height: -webkit-fill-available;
    }
}

/* iOS Safari specific carbon fiber pattern optimization */
@supports (-webkit-touch-callout: none) {
    .racing-header {
        /* Optimize background rendering on iOS */
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        will-change: transform;
    }
}

/* iOS haptic feedback simulation through visual feedback */
.racing-menu-btn:active,
.btn:active {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* iOS Safari specific font loading optimization */
@supports (-webkit-touch-callout: none) {
    .racing-brand, .nav-link {
        font-display: swap;
        -webkit-font-feature-settings: "kern" 1;
        font-feature-settings: "kern" 1;
    }
}

/* iPad-specific mobile navigation mode (portrait) */
.ipad-device.ipad-mobile-nav-mode .racing-header {
    padding: 0.5rem 0 !important;
}

.ipad-device.ipad-mobile-nav-mode .racing-brand {
    font-size: 1.2rem !important;
}

/* Position mobile menu button on the right for iPad */
.ipad-device.ipad-mobile-nav-mode .racing-navbar {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.ipad-device.ipad-mobile-nav-mode .racing-menu-btn {
    margin-left: auto !important;
    order: 999 !important;
}

/* iPad-specific desktop navigation mode (landscape) */
.ipad-device.ipad-desktop-nav-mode .racing-header .container {
    max-width: 100% !important;
    padding-left: 15px !important;
    padding-right: 15px !important;
    width: 100% !important;
}

.ipad-device.ipad-desktop-nav-mode .racing-header .navbar {
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    flex-wrap: nowrap !important;
}

.ipad-device.ipad-desktop-nav-mode .racing-header .racing-nav-link {
    padding: 0.5rem 0.6rem !important;
    font-size: 0.85rem !important;
    letter-spacing: 0.3px !important;
}

.ipad-device.ipad-desktop-nav-mode .racing-brand {
    font-size: 1.3rem !important;
    margin-right: 0.5rem !important;
}

.ipad-device.ipad-desktop-nav-mode .navbar-nav .nav-item:first-child .racing-nav-link {
    margin-left: 1rem !important;
}

.ipad-device.ipad-desktop-nav-mode .racing-notification {
    font-size: 1rem !important;
    padding: 0.4rem !important;
}

.ipad-device.ipad-desktop-nav-mode .racing-nav-link img {
    width: 28px !important;
    height: 28px !important;
}

/* Fix mobile menu button positioning for ALL devices */
.racing-navbar {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.racing-menu-btn.d-lg-none {
    margin-left: auto !important;
    order: 999 !important;
}

/* Ensure brand stays on the left */
.racing-brand {
    order: 1 !important;
}

/* Ensure mobile navigation is properly sized for iPad */
.ipad-device .mobile-nav-bottom {
    padding: 12px 0 !important;
    font-size: 0.9rem !important;
}

.ipad-device .mobile-nav-item {
    padding: 8px 12px !important;
    min-width: 60px !important;
}

.ipad-device .mobile-nav-item i {
    font-size: 1.1rem !important;
}

.ipad-device .mobile-nav-item span {
    font-size: 0.75rem !important;
}

/* Fix logo size for Android devices */
@media (max-width: 768px) {
    .racing-brand {
        font-size: 1.1rem !important;
    }

    .racing-brand img {
        height: 40px !important;
    }
}

.ipad-device .racing-header .dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
    transform: none !important;
    margin-right: 0 !important;
    position: absolute !important;
}

/* Force proper positioning for user menu on iPad */
.ipad-device .racing-header .nav-item.dropdown:last-child {
    position: relative !important;
}

.ipad-device .racing-header .nav-item.dropdown:last-child .dropdown-menu {
    right: 0 !important;
    left: auto !important;
    min-width: 220px !important;
}

/* Ensure notification bell doesn't get cut off */
.ipad-device .racing-notification {
    padding: 0.5rem !important;
    margin-right: 0.25rem !important;
}

/* Additional iPad spacing optimizations */
.ipad-device .racing-header .navbar-nav.me-auto {
    margin-right: 0.3rem !important;
}

.ipad-device .racing-header .navbar-nav:last-child {
    margin-left: auto !important;
}

/* Force compact layout for all nav items */
.ipad-device .racing-header .nav-item {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Compact login/register buttons */
.ipad-device .racing-login-btn,
.ipad-device .racing-register-btn {
    padding: 0.3rem 0.6rem !important;
    font-size: 0.75rem !important;
    margin-left: 0.2rem !important;
}

/* Hide text on smaller elements, keep icons */
.ipad-device .racing-notification .fa-bell::after {
    content: '';
}

/* Ensure dropdown menus don't add extra width */
.ipad-device .racing-header .dropdown-toggle::after {
    margin-left: 0.2rem !important;
}

/* iPad portrait uses PWA mobile navigation - no ultra-compact desktop nav needed */

/* Debug styles for iPad (remove in production) */
.ipad-device .racing-header {
    border: 2px solid red !important;
    box-sizing: border-box !important;
}

.ipad-device .racing-header .container {
    border: 2px solid blue !important;
    box-sizing: border-box !important;
}

.ipad-device .racing-header .navbar {
    border: 2px solid green !important;
    box-sizing: border-box !important;
}

.ipad-device .racing-header .navbar-nav {
    border: 2px solid orange !important;
    box-sizing: border-box !important;
}

.ipad-device .racing-header .navbar-nav:last-child {
    border: 2px solid purple !important;
    box-sizing: border-box !important;
}
