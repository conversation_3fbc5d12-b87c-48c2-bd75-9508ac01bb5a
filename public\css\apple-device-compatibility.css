/**
 * Apple Device Compatibility CSS
 * Enhanced support for iOS Safari, iPhone, iPad, and other Apple devices
 * Version: 1.0.0
 */

/* iOS Safe Area Support for devices with notches (iPhone X and newer) */
:root {
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);
}

/* Header safe area adjustments */
.racing-header {
    padding-top: calc(1rem + var(--safe-area-inset-top));
    padding-left: calc(1rem + var(--safe-area-inset-left));
    padding-right: calc(1rem + var(--safe-area-inset-right));
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
    /* This targets iOS Safari specifically */
    
    /* Prevent iOS Safari from adding rounded corners and shadows to buttons */
    .btn, button, input[type="submit"], input[type="button"] {
        -webkit-appearance: none;
        -webkit-border-radius: 0;
        border-radius: 8px;
        background-clip: padding-box;
    }
    
    /* Fix iOS Safari input styling */
    input[type="text"], 
    input[type="email"], 
    input[type="password"], 
    input[type="tel"], 
    input[type="url"], 
    input[type="search"], 
    textarea, 
    select {
        -webkit-appearance: none;
        -webkit-border-radius: 0;
        border-radius: 4px;
        background-clip: padding-box;
    }
    
    /* Prevent iOS Safari zoom on form inputs */
    input[type="text"], 
    input[type="email"], 
    input[type="password"], 
    input[type="tel"], 
    input[type="url"], 
    input[type="search"], 
    textarea, 
    select {
        font-size: 16px !important;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }
}

/* WebKit specific optimizations */
@media screen and (-webkit-min-device-pixel-ratio: 1) {
    /* High DPI display optimizations */
    .racing-header {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    /* Smooth scrolling for WebKit */
    html {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }
    
    /* Better text rendering on Retina displays */
    body, .racing-brand, .nav-link {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        text-rendering: optimizeLegibility;
    }
}

/* Touch action optimizations for iOS */
.racing-menu-btn, 
.btn, 
button, 
.nav-link, 
.dropdown-toggle {
    touch-action: manipulation;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* iOS specific button active states */
.racing-menu-btn:active,
.btn:active,
button:active {
    transform: scale(0.98);
    -webkit-transform: scale(0.98);
    transition: transform 0.1s ease;
    -webkit-transition: -webkit-transform 0.1s ease;
}

/* Fix iOS Safari modal backdrop issues */
.modal-backdrop {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

/* iOS Safari specific modal fixes */
@supports (-webkit-touch-callout: none) {
    .modal {
        -webkit-overflow-scrolling: touch;
    }
    
    .modal-dialog {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
    }
    
    .modal-content {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
}

/* iPhone landscape orientation fixes */
@media screen and (max-width: 896px) and (orientation: landscape) {
    .racing-header {
        padding-top: calc(0.5rem + var(--safe-area-inset-top));
        min-height: 60px;
    }
    
    .racing-navbar {
        min-height: 50px;
    }
    
    .racing-brand img {
        height: 40px;
    }
}

/* iPad specific optimizations */
@media screen and (min-width: 768px) and (max-width: 1024px) {
    .racing-header {
        padding-top: calc(1.5rem + var(--safe-area-inset-top));
    }
    
    /* Better touch targets for iPad */
    .nav-link {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    .btn {
        min-height: 44px;
        padding: 12px 20px;
    }
}

/* iOS PWA specific styles */
@media (display-mode: standalone) {
    /* When app is installed as PWA on iOS */
    body {
        padding-top: var(--safe-area-inset-top);
        padding-bottom: var(--safe-area-inset-bottom);
    }
    
    .racing-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1030;
        padding-top: calc(1rem + var(--safe-area-inset-top));
    }
    
    /* Adjust main content for fixed header */
    main, .main-content {
        margin-top: calc(80px + var(--safe-area-inset-top));
    }
}

/* iOS Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .racing-header {
        border-bottom-color: #444;
    }
    
    .racing-brand, .nav-link {
        color: #e0e0e0 !important;
    }
}

/* iOS Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .racing-menu-btn:active,
    .btn:active,
    button:active {
        transform: none;
        -webkit-transform: none;
        transition: none;
        -webkit-transition: none;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* iOS VoiceOver and accessibility improvements */
@media (prefers-reduced-motion: no-preference) {
    .racing-menu-btn:focus,
    .btn:focus,
    .nav-link:focus {
        outline: 2px solid #007AFF;
        outline-offset: 2px;
    }
}

/* Fix iOS Safari viewport height issues */
@supports (-webkit-touch-callout: none) {
    .min-vh-100 {
        min-height: -webkit-fill-available;
    }
    
    html {
        height: -webkit-fill-available;
    }
}

/* iOS Safari specific carbon fiber pattern optimization */
@supports (-webkit-touch-callout: none) {
    .racing-header {
        /* Optimize background rendering on iOS */
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        will-change: transform;
    }
}

/* iOS haptic feedback simulation through visual feedback */
.racing-menu-btn:active,
.btn:active {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* iOS Safari specific font loading optimization */
@supports (-webkit-touch-callout: none) {
    .racing-brand, .nav-link {
        font-display: swap;
        -webkit-font-feature-settings: "kern" 1;
        font-feature-settings: "kern" 1;
    }
}
