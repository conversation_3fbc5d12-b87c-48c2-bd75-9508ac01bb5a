<?php
/**
 * Header Settings API Endpoint
 * Returns header settings for different device types
 */

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Include necessary files
require_once '../config/config.php';
require_once '../models/SettingsModel.php';

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'error' => 'Method not allowed'
        ]);
        exit;
    }
    
    // Initialize settings model
    $settingsModel = new SettingsModel();
    
    // Get all header settings
    $settings = [
        'sticky_header_desktop' => $settingsModel->getSetting('sticky_header_desktop', '1'),
        'sticky_header_ipad_portrait' => $settingsModel->getSetting('sticky_header_ipad_portrait', '0'),
        'sticky_header_ipad_landscape' => $settingsModel->getSetting('sticky_header_ipad_landscape', '1'),
        'sticky_header_android_portrait' => $settingsModel->getSetting('sticky_header_android_portrait', '0'),
        'sticky_header_android_landscape' => $settingsModel->getSetting('sticky_header_android_landscape', '1'),
        'sticky_header_iphone_portrait' => $settingsModel->getSetting('sticky_header_iphone_portrait', '0'),
        'sticky_header_iphone_landscape' => $settingsModel->getSetting('sticky_header_iphone_landscape', '1')
    ];
    
    // Return success response
    echo json_encode([
        'success' => true,
        'settings' => $settings,
        'timestamp' => time()
    ]);
    
} catch (Exception $e) {
    // Log error
    error_log('Header Settings API Error: ' . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error',
        'message' => DEBUG_MODE ? $e->getMessage() : 'An error occurred'
    ]);
}
?>
