/**
 * Custom Calendar CSS
 * 
 * A fully responsive, custom-built calendar system for Events and Shows Management System
 * 
 * Version 1.0.0
 */

/* Global box-sizing for consistent sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Calendar Container */
.custom-calendar {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 100%;
  position: relative;
  box-sizing: border-box;
}

/* Calendar Header */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.calendar-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 10px;
}

.calendar-nav-btn {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-nav-btn:hover {
  background-color: #f1f3f5;
}

.calendar-nav-btn.today {
  background-color: #3788d8;
  color: white;
  border-color: #3788d8;
}

.calendar-nav-btn.today:hover {
  background-color: #2d6fae;
}

/* Calendar View Selector */
.calendar-view-selector {
  display: flex;
  gap: 5px;
  margin-bottom: 15px;
}

.calendar-view-btn {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-view-btn:hover {
  background-color: #e9ecef;
}

.calendar-view-btn.active {
  background-color: #3788d8;
  color: white;
  border-color: #3788d8;
}

/* Month View */
.calendar-month {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-top: 1px solid #e9ecef;
  border-left: 1px solid #e9ecef;
  position: relative; /* For multi-day event positioning */
}

/* Multi-day events container */
.calendar-multi-day-events {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: auto repeat(6, 1fr); /* Weekday header + up to 6 weeks */
  pointer-events: none; /* Allow clicks to pass through to day cells */
  z-index: 10;
  gap: 0;
}

/* Spanning event elements */
.calendar-spanning-event {
  pointer-events: auto; /* Re-enable clicks on events */
  margin: 2px 1px;
  height: 18px;
  display: flex;
  align-items: center;
  border-radius: 9px; /* Make ends half-circle round (height/2) */
  font-size: 0.8rem;
  color: white;
  padding: 2px 8px; /* Slightly more padding for rounded ends */
  overflow: hidden; /* Hide overflow for scrolling text */
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  align-self: start; /* Align to top of grid cell */
  /* Subtle text shadow for better readability on light backgrounds */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
  /* Consistent positioning */
  position: relative;
  top: 2px; /* Consistent vertical positioning */
}

/* Scrolling text animation for long event titles - both spanning and single-day */
.calendar-spanning-event .calendar-event-title,
.calendar-event .calendar-event-title {
  display: inline-block;
  animation: scroll-text-continuous 6s linear infinite;
  animation-play-state: paused; /* Start paused */
}

.calendar-spanning-event:hover .calendar-event-title,
.calendar-event:hover .calendar-event-title {
  animation-play-state: running; /* Start scrolling on hover */
}

/* Continuous right-to-left scrolling animation */
@keyframes scroll-text-continuous {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/* Segment styling for multi-row events - keep rounded ends */
.calendar-spanning-event.segment-start {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 9px; /* Keep left side rounded */
  border-bottom-left-radius: 9px; /* Keep left side rounded */
  margin-right: 0;
}

.calendar-spanning-event.segment-middle {
  border-radius: 0; /* Middle segments are rectangular */
  margin-left: 0;
  margin-right: 0;
}

.calendar-spanning-event.segment-end {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 9px; /* Keep right side rounded */
  border-bottom-right-radius: 9px; /* Keep right side rounded */
  margin-left: 0;
}

/* Multi-day event styling (simplified approach) */
.calendar-multi-day-event {
  border-left: 4px solid rgba(255, 255, 255, 0.8);
  position: relative;
}

.calendar-multi-day-event::after {
  content: '→';
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.7rem;
  opacity: 0.8;
  color: rgba(255, 255, 255, 0.9);
}

.calendar-event-duration {
  font-size: 0.7rem;
  opacity: 0.8;
  font-weight: normal;
}

/* Spanning bars for multi-day events */
.calendar-spanning-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
}

.calendar-spanning-bar {
  position: absolute;
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8rem;
  color: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  pointer-events: auto;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.calendar-spanning-bar:hover {
  opacity: 0.9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Overlay spanning events (proper approach) */
.calendar-overlay-spanning-event {
  margin: 25px 2px 2px 2px; /* Top margin to clear day numbers */
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8rem;
  color: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  height: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  pointer-events: auto;
  transition: opacity 0.2s ease, box-shadow 0.2s ease;
}

.calendar-overlay-spanning-event:hover {
  opacity: 0.9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* CSS Grid spanning events (DEPRECATED - caused layout issues) */
.calendar-grid-spanning-event {
  margin: 2px 1px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8rem;
  color: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  height: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: opacity 0.2s ease, box-shadow 0.2s ease;
}

.calendar-grid-spanning-event:hover {
  opacity: 0.9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.calendar-weekday {
  padding: 10px;
  text-align: center;
  font-weight: 600;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

.calendar-day {
  min-height: 120px; /* Increased for better event positioning */
  height: 120px; /* Fixed height for equal sizing */
  padding: 5px;
  border-right: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent content from expanding cell */
}

.calendar-day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.calendar-day-number {
  font-weight: 600;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.calendar-day.today .calendar-day-number {
  background-color: #3788d8;
  color: white;
}

.calendar-day.other-month {
  background-color: #f8f9fa;
  color: #adb5bd;
}

.calendar-day.weekend {
  background-color: #f8f9fa;
}

.calendar-day-events {
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
  flex: 1; /* Take remaining space after header */
  position: relative; /* For time-based positioning */
}

/* All-day events section at top of day cell */
.calendar-all-day-events {
  display: flex;
  flex-direction: column;
  gap: 1px;
  margin-bottom: 3px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 2px;
}

/* Time-based events section */
.calendar-timed-events {
  flex: 1;
  position: relative;
  min-height: 60px; /* Minimum space for timed events */
}

/* Week View */
.calendar-week {
  display: flex;
  flex-direction: column;
}

.calendar-week-header {
  display: grid;
  grid-template-columns: 60px repeat(7, 1fr);
  border-top: 1px solid #e9ecef;
  border-left: 1px solid #e9ecef;
}

.calendar-week-header-cell {
  padding: 10px;
  text-align: center;
  font-weight: 600;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

.calendar-week-body {
  display: grid;
  grid-template-columns: 60px repeat(7, 1fr);
  border-left: 1px solid #e9ecef;
  height: 800px; /* Increased height */
  overflow-y: auto;
  position: relative; /* Ensure proper positioning context */
  min-height: 1200px; /* 24 hours * 50px per hour */
}

.calendar-week-times {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e9ecef;
}

.calendar-week-time {
  height: 50px;
  padding: 2px 5px;
  text-align: right;
  font-size: 0.8rem;
  color: #6c757d;
  border-bottom: 1px solid #e9ecef;
}

.calendar-week-day {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e9ecef;
  position: relative;
  /* Fix for event positioning */
  min-height: 1200px; /* 24 hours * 50px per hour */
}

.calendar-week-hour {
  height: 50px;
  border-bottom: 1px solid #e9ecef;
  position: relative;
  box-sizing: border-box; /* Ensure consistent sizing */
}

.calendar-week-hour.half-hour {
  border-bottom: 1px dashed #e9ecef;
}

/* Responsive adjustments for week hours */
@media (max-width: 576px) {
  .calendar-week-hour {
    height: 40px; /* Smaller height on mobile */
  }
}

/* Day View */
.calendar-day-view {
  display: grid;
  grid-template-columns: 60px 1fr;
  border-left: 1px solid #e9ecef;
  height: 800px; /* Increased height */
  overflow-y: auto;
  position: relative; /* Ensure proper positioning context */
  min-height: 1200px; /* 24 hours * 50px per hour */
}

.calendar-day-times {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e9ecef;
}

.calendar-day-time {
  height: 50px;
  padding: 2px 5px;
  text-align: right;
  font-size: 0.8rem;
  color: #6c757d;
  border-bottom: 1px solid #e9ecef;
}

.calendar-day-content {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e9ecef;
  position: relative;
  /* Fix for event positioning */
  min-height: 1200px; /* 24 hours * 50px per hour */
}

.calendar-day-hour {
  height: 50px;
  border-bottom: 1px solid #e9ecef;
  position: relative;
  box-sizing: border-box; /* Ensure consistent sizing */
}

.calendar-day-hour.half-hour {
  border-bottom: 1px dashed #e9ecef;
}

/* Responsive adjustments for day hours */
@media (max-width: 576px) {
  .calendar-day-hour {
    height: 40px; /* Smaller height on mobile */
  }
}

/* List View */
.calendar-list-view {
  display: flex;
  flex-direction: column;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  background-color: #fff;
}

.calendar-list-header {
  display: grid;
  grid-template-columns: 120px 1fr 150px;
  padding: 12px 20px;
  background-color: #f1f5f9;
  font-weight: 600;
  border-bottom: 2px solid #e2e8f0;
  color: #334155;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

.calendar-list-day {
  margin: 15px 0;
}

.calendar-list-day-header {
  padding: 12px 20px;
  background-color: #f8fafc;
  font-weight: 700;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  color: #1e293b;
  font-size: 1.1rem;
  letter-spacing: 0.3px;
  position: sticky;
  top: 0;
  z-index: 5;
  display: flex;
  align-items: center;
  gap: 10px;
}

.calendar-list-day-header.today {
  background-color: #ebf5ff;
  border-left: 3px solid #3b82f6;
  padding-left: 17px; /* 20px - 3px border */
}

.calendar-list-day-header .today-badge {
  background-color: #3b82f6;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.calendar-list-day-header .day-name {
  font-weight: 700;
  color: #1e293b;
}

.calendar-list-day-header .day-date {
  font-weight: 500;
  color: #64748b;
  margin-left: auto;
}

.calendar-list-events {
  display: flex;
  flex-direction: column;
  gap: 2px; /* Small gap between events */
}

/* No events message */
.calendar-list-no-events {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #64748b;
  background-color: #f8fafc;
  border-radius: 8px;
  margin: 20px;
}

.calendar-list-no-events .no-events-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.calendar-list-no-events h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 10px 0;
}

.calendar-list-no-events p {
  font-size: 0.95rem;
  margin: 0;
  max-width: 300px;
}

/* Events */
.calendar-event {
  padding: 2px 6px; /* Match spanning events padding */
  border-radius: 9px; /* Match spanning events rounded ends */
  font-size: 0.8rem; /* Match spanning events font size */
  color: white;
  margin-bottom: 2px;
  cursor: pointer;
  overflow: hidden; /* Hide overflow for scrolling text */
  white-space: nowrap;
  position: relative;
  flex-shrink: 0; /* Prevent events from shrinking too much */
  height: 18px; /* Match spanning events exact height */
  max-width: 100%; /* Prevent horizontal overflow */
  box-sizing: border-box; /* Include padding in width calculation */
  display: flex;
  align-items: center; /* Center text vertically */
  /* Subtle text shadow for better readability on light backgrounds */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
  /* Consistent positioning */
  top: 2px; /* Match spanning events positioning */
}

/* All-day events styling */
.calendar-event.all-day {
  background-color: #3788d8;
  width: 100%; /* Full width for all-day events */
  margin-left: 0;
  margin-right: 0;
}

/* Timed events positioning based on time */
.calendar-event.timed {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 5;
}

.calendar-event.all-day {
  background-color: #3788d8;
}

/* Multi-day event spanning */
.calendar-event.multi-day {
  position: relative;
  border-radius: 0; /* Remove border radius for spanning events */
}

.calendar-event.multi-day.start {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}

.calendar-event.multi-day.end {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.calendar-event.multi-day.middle {
  border-radius: 0;
}

/* Multi-day event continuation indicators - subtle visual cues */
.calendar-event.multi-day.start::after {
  content: '▶';
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.6rem;
  opacity: 0.6;
}

.calendar-event.multi-day.end::before {
  content: '◀';
  position: absolute;
  left: 2px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.6rem;
  opacity: 0.6;
}

.calendar-event.multi-day.middle::before {
  content: '◀';
  position: absolute;
  left: 2px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.6rem;
  opacity: 0.6;
}

.calendar-event.multi-day.middle::after {
  content: '▶';
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.6rem;
  opacity: 0.6;
}

/* Adjust text padding for multi-day events to accommodate indicators */
.calendar-event.multi-day.start .calendar-event-title {
  padding-right: 12px;
  max-width: calc(100% - 12px);
}

.calendar-event.multi-day.end .calendar-event-title {
  padding-left: 12px;
  max-width: calc(100% - 12px);
}

.calendar-event.multi-day.middle .calendar-event-title {
  padding-left: 12px;
  padding-right: 12px;
  max-width: calc(100% - 24px);
}

/* Ensure event time doesn't interfere with indicators */
.calendar-event.multi-day .calendar-event-time {
  flex-shrink: 0;
  margin-right: 4px;
}

.calendar-event-time {
  font-size: 0.75rem;
  opacity: 0.9;
}

.calendar-event-title {
  font-weight: 500;
}

.calendar-event-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

/* Time-based events for week and day views */
.calendar-time-event {
  position: absolute;
  left: 2px;
  right: 2px;
  border-radius: 3px;
  padding: 3px 5px;
  font-size: 0.85rem;
  color: white;
  overflow: hidden;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  margin: 1px;
  min-height: 20px;
  /* Fix for event positioning */
  pointer-events: auto;
}

/* List view events */
.calendar-list-event {
  display: grid;
  grid-template-columns: 120px 1fr 150px;
  padding: 14px 20px;
  border-bottom: 1px solid #f1f5f9;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  margin: 0 4px 4px 4px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  background-color: #ffffff;
}

.calendar-list-event:hover {
  background-color: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.calendar-list-event:hover:before {
  width: 8px;
  transition: width 0.2s ease;
}

.calendar-list-event:hover .calendar-list-event-title {
  color: #0f172a;
}

.calendar-list-event:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  background-color: var(--event-color, #3788d8); /* Use custom color if available */
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

/* Different colors for alternating events when no custom color is provided */
.calendar-list-event:not([style*="--event-color"]):nth-child(4n+1):before { background-color: #3788d8; /* Blue */ }
.calendar-list-event:not([style*="--event-color"]):nth-child(4n+2):before { background-color: #28a745; /* Green */ }
.calendar-list-event:not([style*="--event-color"]):nth-child(4n+3):before { background-color: #dc3545; /* Red */ }
.calendar-list-event:not([style*="--event-color"]):nth-child(4n+4):before { background-color: #fd7e14; /* Orange */ }

.calendar-list-event-time {
  font-size: 0.9rem;
  color: #64748b;
  font-weight: 500;
  padding-left: 10px;
}

.calendar-list-event-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 1.05rem;
}

.calendar-list-event-description {
  font-weight: 400;
  color: #64748b;
  font-size: 0.85rem;
  margin-top: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.calendar-list-event-location {
  font-size: 0.9rem;
  color: #64748b;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 5px;
}

.calendar-list-event-location:before {
  content: '📍'; /* Location pin emoji */
  font-size: 0.9rem;
}

/* More events indicator */
.calendar-more-events {
  font-size: 0.8rem;
  text-align: center;
  padding: 2px;
  background-color: #f8f9fa;
  border-radius: 3px;
  cursor: pointer;
  color: #6c757d;
}

/* Event Modal */
.calendar-event-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.calendar-event-modal.show {
  opacity: 1;
  visibility: visible;
}

.calendar-event-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.calendar-event-modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-event-modal-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.calendar-event-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.calendar-event-modal-body {
  padding: 20px;
}

.calendar-event-modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Calendar Sidebar */
.calendar-sidebar {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.calendar-sidebar-section {
  margin-bottom: 20px;
}

.calendar-sidebar-header {
  padding: 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.calendar-sidebar-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.calendar-sidebar-body {
  padding: 15px;
}

.calendar-list {
  max-height: 200px;
  overflow-y: auto;
}

.calendar-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.calendar-color-dot {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

/* Upcoming Events */
.upcoming-events {
  max-height: 300px;
  overflow-y: auto;
}

.upcoming-event {
  padding: 10px 15px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.upcoming-event:hover {
  background-color: #f8f9fa;
}

.upcoming-event-date {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 3px;
}

.upcoming-event-title {
  font-weight: 500;
  margin-bottom: 3px;
}

.upcoming-event-location {
  font-size: 0.8rem;
  color: #6c757d;
}

/* Event Table Below Calendar */
.calendar-events-table {
  margin-top: 30px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.calendar-events-table-header {
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 2px solid #e9ecef;
}

.calendar-events-table-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.calendar-events-table-content {
  overflow-x: auto;
}

.events-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.events-table th {
  background-color: #f1f5f9;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  color: #334155;
  border-bottom: 2px solid #e2e8f0;
  white-space: nowrap;
}

.events-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #f1f5f9;
  vertical-align: top;
}

.events-table tbody tr {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.events-table tbody tr:hover {
  background-color: #f8fafc;
}

.event-title-cell {
  font-weight: 500;
  color: #1e293b;
  max-width: 200px;
}

.event-location-cell {
  color: #64748b;
  max-width: 150px;
}

.event-date-cell {
  color: #475569;
  white-space: nowrap;
}

.event-time-cell {
  color: #475569;
  white-space: nowrap;
}

.event-venue-cell {
  color: #64748b;
  max-width: 150px;
}

/* Event Hover Popup */
.event-hover-popup {
  position: absolute;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 15px;
  max-width: 300px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  pointer-events: none;
}

.event-hover-popup.show {
  opacity: 1;
  visibility: visible;
}

.event-hover-popup h4 {
  margin: 0 0 10px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
}

.event-hover-popup .popup-detail {
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #64748b;
}

.event-hover-popup .popup-detail strong {
  color: #374151;
}

.event-hover-popup .popup-description {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f1f5f9;
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.4;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .calendar-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .calendar-title {
    font-size: 1.3rem;
  }
  
  .calendar-week-header, .calendar-week-body {
    grid-template-columns: 50px repeat(7, 1fr);
  }
  
  .calendar-day-view {
    grid-template-columns: 50px 1fr;
  }
  
  .events-table {
    font-size: 0.8rem;
  }
  
  .events-table th,
  .events-table td {
    padding: 8px 10px;
  }
}

@media (max-width: 768px) {
  .calendar-view-selector {
    overflow-x: auto;
    padding-bottom: 5px;
  }
  
  .calendar-day {
    min-height: 80px;
  }
  
  /* Adjust week view for mobile - make columns narrower */
  .calendar-week-header, .calendar-week-body {
    grid-template-columns: 30px repeat(7, 1fr);
    width: 100%;
    max-width: 100%;
  }
  
  .calendar-week-header-cell {
    padding: 3px;
    font-size: 0.7rem;
  }
  
  /* Make day names shorter */
  .calendar-week-header-cell div:first-child {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .calendar-week-time {
    font-size: 0.7rem;
    padding: 2px;
  }
  
  .calendar-day-view {
    grid-template-columns: 30px 1fr;
  }
  
  .calendar-list-header, .calendar-list-event {
    grid-template-columns: 100px 1fr 100px;
  }
}

@media (max-width: 576px) {
  .calendar-month {
    font-size: 0.9rem;
  }
  
  .calendar-day {
    min-height: 60px;
    padding: 2px;
  }
  
  .calendar-day-number {
    width: 20px;
    height: 20px;
    font-size: 0.8rem;
  }
  
  .calendar-event {
    font-size: 0.75rem;
    padding: 2px 3px;
  }
  
  /* Ultra-compact week view for mobile */
  .calendar-week-body {
    height: 500px;
    grid-template-columns: 20px repeat(7, 1fr);
  }
  
  .calendar-week-header {
    grid-template-columns: 20px repeat(7, 1fr);
  }
  
  .calendar-week-header-cell {
    padding: 2px 0;
    font-size: 0.65rem;
    text-align: center;
  }
  
  /* Show only first letter of day name */
  .calendar-week-header-cell div:first-child {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.65rem;
  }
  
  /* Use abbreviated day names */
  .calendar-week-header-cell div:first-child::first-letter {
    font-weight: bold;
  }
  
  .calendar-week-time {
    font-size: 0.6rem;
    padding: 0;
    text-align: center;
  }
  
  .calendar-day-view {
    height: 500px;
    grid-template-columns: 20px 1fr;
  }
  
  .calendar-day-time {
    font-size: 0.6rem;
    padding: 0;
    text-align: center;
  }
  
  .calendar-list-header, .calendar-list-event {
    grid-template-columns: 80px 1fr;
  }
  
  .calendar-list-event-location {
    display: none;
  }
  
  /* Improve time event display on small screens */
  .calendar-time-event {
    font-size: 0.65rem;
    padding: 1px;
  }
  
  .calendar-event-title {
    font-size: 0.65rem;
  }
  
  .calendar-event-time {
    font-size: 0.6rem;
  }
}

/* Compact Club Display Styles */
.event-clubs-banner {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.event-clubs-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.1) 75%, transparent 75%, transparent);
  background-size: 20px 20px;
  opacity: 0.3;
}

.event-clubs-banner:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 123, 255, 0.4);
}

.club-badge {
  background: rgba(255, 255, 255, 0.95) !important;
  color: #007bff !important;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  font-weight: 600 !important;
  text-shadow: none;
  backdrop-filter: blur(10px);
}

.club-badge:hover {
  background: #ffffff !important;
  transform: scale(1.05);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.club-badge i {
  color: #007bff;
  text-shadow: none;
}

/* Animation for club badges */
@keyframes clubBadgeSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.club-badge {
  animation: clubBadgeSlideIn 0.5s ease forwards;
}

.club-badge:nth-child(1) { animation-delay: 0.1s; }
.club-badge:nth-child(2) { animation-delay: 0.2s; }
.club-badge:nth-child(3) { animation-delay: 0.3s; }
.club-badge:nth-child(4) { animation-delay: 0.4s; }
.club-badge:nth-child(5) { animation-delay: 0.5s; }
.club-badge:nth-child(6) { animation-delay: 0.6s; }

/* Responsive adjustments for compact club display */
@media (max-width: 768px) {
  .event-clubs-banner {
    padding: 1rem !important;
  }
  
  .event-clubs-banner h5 {
    font-size: 1.1rem;
  }
  
  .club-badge {
    font-size: 0.85rem !important;
    padding: 0.4rem 0.8rem !important;
  }
  
  .event-clubs-banner .fas.fa-users {
    font-size: 1.5rem !important;
  }
}

@media (max-width: 576px) {
  .event-clubs-banner {
    padding: 0.75rem !important;
  }
  
  .event-clubs-banner .d-flex {
    flex-direction: column;
    text-align: center;
  }
  
  .event-clubs-banner .me-3 {
    margin-right: 0 !important;
    margin-bottom: 0.75rem;
  }
  
  .club-badge {
    margin-bottom: 0.5rem;
    display: inline-block;
  }
}

/* Print Styles */
@media print {
  .calendar-nav, .calendar-view-selector, .calendar-sidebar {
    display: none;
  }
  
  .calendar-month, .calendar-week, .calendar-day-view, .calendar-list-view {
    border: 1px solid #000;
  }
  
  .calendar-day, .calendar-weekday, .calendar-week-header-cell, 
  .calendar-week-time, .calendar-day-time, .calendar-list-header,
  .calendar-list-event {
    border-color: #000;
  }
  
  /* Print styles for club display */
  .event-clubs-section {
    background: #f8f9fa !important;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
  
  .club-item {
    background: #fff !important;
    border: 1px solid #000 !important;
    box-shadow: none !important;
  }
}

/* Popup Clubs Styling for Monthly Chart Hover Popups */
.popup-clubs-section {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e9ecef;
}

.clubs-label-popup {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6c757d;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.clubs-list-popup {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.club-badge-popup {
  display: inline-block;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #495057;
  font-size: 0.7rem;
  font-weight: 500;
  padding: 3px 8px;
  border-radius: 12px;
  border: 1px solid #dee2e6;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: default;
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.club-badge-popup:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
  color: #212529;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-color: #adb5bd;
}

/* Responsive adjustments for popup clubs */
@media (max-width: 575.98px) {
  .club-badge-popup {
    font-size: 0.65rem;
    padding: 2px 6px;
  }
  
  .clubs-label-popup {
    font-size: 0.7rem;
  }
}